<div class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">Trading Rules</h1>
            <p class="text-gray-500 dark:text-gray-400 mt-1 sm:mt-2 text-sm sm:text-base">Define and track your trading rules to improve consistency</p>
        </div>
        <button id="createRuleBtn" class="w-full sm:w-auto flex items-center justify-center px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl bg-slate-600 dark:bg-slate-700 text-white font-medium hover:bg-slate-700 dark:hover:bg-slate-600 transition-all duration-300 text-sm sm:text-base">
            <i class="fas fa-plus w-4 h-4 sm:w-5 sm:h-5 mr-2"></i> Create New Rule
        </button>
    </div>
    
    <!-- Rule Selector -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white mb-3 sm:mb-0">Your Rule Collection</h2>
            <div class="relative w-full sm:w-64">
                <input type="text" id="searchRules" placeholder="Search rules..." class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl py-2 px-4 text-sm sm:text-base text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300">
                <i class="fas fa-search absolute right-3 top-2.5 text-gray-400 text-sm"></i>
            </div>
        </div>
        
        <div class="flex flex-wrap gap-3 mb-6">
            <button class="filter-btn inline-flex items-center px-4 py-2 rounded-xl bg-gray-700 text-sm text-white border border-gray-600 hover:bg-gray-600 hover:border-gray-500 transition-all duration-300 cursor-pointer shadow-lg shadow-gray-500/10 active" data-filter="all">
                All Rules <span id="totalRulesCount" class="ml-2 text-gray-400">(0)</span>
            </button>
            <button class="filter-btn inline-flex items-center px-4 py-2 rounded-xl bg-gray-800 text-sm text-blue-400 border border-gray-700 hover:bg-blue-500/10 hover:border-blue-500/50 hover:shadow-blue-500/20 transition-all duration-300 cursor-pointer shadow-lg" data-filter="most-used">
                Most Used <i class="fas fa-trending-up w-4 h-4 ml-2"></i>
            </button>
            <button class="filter-btn inline-flex items-center px-4 py-2 rounded-xl bg-gray-800 text-sm text-purple-400 border border-gray-700 hover:bg-purple-500/10 hover:border-purple-500/50 hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer shadow-lg" data-filter="high-impact">
                High Impact <i class="fas fa-bolt w-4 h-4 ml-2"></i>
            </button>
            <button class="filter-btn inline-flex items-center px-4 py-2 rounded-xl bg-gray-800 text-sm text-green-400 border border-gray-700 hover:bg-green-500/10 hover:border-green-500/50 hover:shadow-green-500/20 transition-all duration-300 cursor-pointer shadow-lg" data-filter="new">
                New <i class="fas fa-sparkles w-4 h-4 ml-2"></i>
            </button>
        </div>
        
        <!-- Rules Grid -->
        <div id="rulesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Rules will be loaded here via AJAX -->
        </div>
        
        <!-- Add New Rule Card -->
        <div class="flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-xl p-5 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-500 transition-all duration-300 cursor-pointer mt-4" id="addRuleCard">
            <div class="text-center">
                <i class="fas fa-plus-circle w-8 h-8 mx-auto text-gray-400 mb-2"></i>
                <p class="text-gray-500 dark:text-gray-400">Add new rule</p>
            </div>
        </div>
    </div>
    
    <!-- Rules Analysis Section -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
            <div class="flex items-center space-x-3 mb-4 sm:mb-0">
                <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-sm sm:text-lg"></i>
                </div>
                <div>
                    <h2 class="text-lg sm:text-2xl font-bold text-gray-800 dark:text-white">Rules Performance Analysis</h2>
                    <p class="text-gray-500 dark:text-gray-400 text-xs sm:text-sm">Track your trading discipline and rule adherence</p>
                </div>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                <select id="analyticsTimeframe" class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors">
                    <option value="7">Last 7 days</option>
                    <option value="30" selected>Last 30 days</option>
                    <option value="90">Last 90 days</option>
                    <option value="365">Last year</option>
                </select>
                <button id="refreshAnalytics" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg">
                    <i class="fas fa-sync-alt w-3 h-3 sm:w-4 sm:h-4 mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
        

        
        <!-- Top Rules Chart -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            <div class="bg-gradient-to-br from-slate-50/80 to-slate-100/80 dark:from-slate-800/60 dark:to-slate-700/60 rounded-xl p-4 sm:p-6 lg:p-7 border border-slate-200/50 dark:border-slate-600/30 shadow-md hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                <div class="flex items-center space-x-3 mb-4 sm:mb-6 lg:mb-7">
                    <div class="w-7 h-7 sm:w-8 sm:h-8 rounded-lg bg-gradient-to-br from-indigo-500/80 to-violet-600/80 flex items-center justify-center shadow-sm">
                        <i class="fas fa-trophy text-white text-xs sm:text-sm"></i>
                    </div>
                    <h3 class="text-base sm:text-lg font-bold text-slate-800 dark:text-slate-100">Top 5 Most Followed Rules</h3>
                </div>
                <div id="topRulesList" class="space-y-4">
                    <!-- Top rules will be loaded here -->
                </div>
            </div>

            <div class="bg-gradient-to-br from-slate-50/80 to-slate-100/80 dark:from-slate-800/60 dark:to-slate-700/60 rounded-xl p-4 sm:p-6 lg:p-7 border border-slate-200/50 dark:border-slate-600/30 shadow-md hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                <div class="flex items-center space-x-3 mb-4 sm:mb-6 lg:mb-7">
                    <div class="w-7 h-7 sm:w-8 sm:h-8 rounded-lg bg-gradient-to-br from-orange-500/80 to-red-600/80 flex items-center justify-center shadow-sm">
                        <i class="fas fa-exclamation-circle text-white text-xs sm:text-sm"></i>
                    </div>
                    <h3 class="text-base sm:text-lg font-bold text-slate-800 dark:text-slate-100">Least Used Rules</h3>
                </div>
                <div class="mt-6" id="leastUsedRulesContainer">
                    <!-- Least used rules will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Rule Modal -->
<div id="createRuleModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4 transform transition-all">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Create New Rule</h2>
            <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times w-6 h-6"></i>
            </button>
        </div>
        
        <form id="ruleForm">
            <div class="mb-4">
                <label for="ruleName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rule Name</label>
                <input type="text" id="ruleName" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl py-2 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300" placeholder="e.g., Wait for confirmation" required>
            </div>
            
            <div class="mb-4">
                <label for="ruleCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                <select id="ruleCategory" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl py-2 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300">
                    <option value="entry">Entry</option>
                    <option value="exit">Exit</option>
                    <option value="risk_management">Risk Management</option>
                    <option value="psychology">Psychology</option>
                    <option value="analysis">Analysis</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
            
            <div class="mb-6">
                <label for="ruleDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea id="ruleDescription" rows="4" class="w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl py-2 px-4 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300" placeholder="Describe the rule in detail..."></textarea>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelRuleBtn" class="px-4 py-2 rounded-xl bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-all duration-300">
                    Cancel
                </button>
                <button type="submit" class="px-6 py-2 rounded-xl bg-slate-600 dark:bg-slate-700 text-white font-medium hover:bg-slate-700 dark:hover:bg-slate-600 transition-all duration-300">
                    Save Rule
                </button>
            </div>
        </form>
    </div>
</div>
