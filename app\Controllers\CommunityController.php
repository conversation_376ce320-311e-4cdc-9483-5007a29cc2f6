<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PostModel;
use App\Models\CommentModel;
use App\Models\LikeModel;
use App\Models\FollowModel;
use App\Models\UserModel;
use App\Models\NotificationModel;
use Config\Upload;

class CommunityController extends BaseController
{
    protected $postModel;
    protected $commentModel;
    protected $likeModel;
    protected $followModel;
    protected $userModel;
    protected $notificationModel;
    protected $uploadConfig;

    public function __construct()
    {
        $this->postModel = new PostModel();
        $this->commentModel = new CommentModel();
        $this->likeModel = new LikeModel();
        $this->followModel = new FollowModel();
        $this->userModel = new UserModel();
        $this->notificationModel = new NotificationModel();
        $this->uploadConfig = new Upload();

        // Fix existing posts with wrong image URLs (run once)
        // Temporarily disabled to prevent URL corruption
        // $this->fixExistingImageUrls();
    }

    /**
     * Display community feed
     */
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $data['title'] = 'Community Feed';
        $data['active'] = 'community';
        $data['userDetails'] = $this->userModel->find($userId);
        $data['customScript'] = 'community';
        $data['main_content'] = 'pages/community';

        return view('includes/template', $data);
    }

    /**
     * Get posts for feed (AJAX)
     */
    public function getPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;
        
        $filters = [
            'post_type' => $this->request->getGet('post_type') ?? 'all',
            'asset_class' => $this->request->getGet('asset_class') ?? 'all',
            'sort' => $this->request->getGet('sort') ?? 'latest',
            'following_only' => $this->request->getGet('following_only') ?? false
        ];

        if ($filters['following_only']) {
            $posts = $this->followModel->getFollowingPosts($userId, $limit, $offset);
        } else {
            $posts = $this->postModel->getPostsWithUserInfo($limit, $offset, $filters);
        }

        // Get like status for current user
        $likeItems = [];
        foreach ($posts as $post) {
            $likeItems[] = ['type' => 'post', 'id' => $post['id']];
        }
        $likedItems = $this->likeModel->getLikeStatusForItems($userId, $likeItems);

        // Get follow status for post authors
        $authorIds = array_column($posts, 'user_id');
        $followingUsers = $this->followModel->getFollowStatusForUsers($userId, $authorIds);

        // Add status to posts
        foreach ($posts as &$post) {
            $post['is_liked'] = isset($likedItems['post_' . $post['id']]);
            $post['is_following'] = isset($followingUsers[$post['user_id']]);
            $post['is_own_post'] = $post['user_id'] == $userId;

            // Convert relative image path to full URL for display
            if (!empty($post['image_url'])) {
                $post['image_url'] = $this->uploadConfig->convertToFullUrl($post['image_url']);
                log_message('debug', "Post {$post['id']} image URL: {$post['image_url']}");
            }

            // Format time ago
            $post['time_ago'] = $this->timeAgo($post['created_at']);

            // Parse tags
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Create new post
     */
    public function createPost()
    {
        // Add logging to debug duplication
        log_message('info', 'CreatePost called at: ' . date('Y-m-d H:i:s.u'));

        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        log_message('info', 'Creating post for user: ' . $userId);
        
        // Check for duplicate submission using session token
        $submissionToken = $this->request->getPost('submission_token');
        $lastToken = session('last_submission_token');

        if ($submissionToken && $submissionToken === $lastToken) {
            log_message('info', 'Duplicate submission detected with token: ' . $submissionToken);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Duplicate submission detected'
            ]);
        }

        // Store the token to prevent future duplicates
        session()->set('last_submission_token', $submissionToken);

        $validation = \Config\Services::validation();
        $validation->setRules([
            'title' => 'required|max_length[255]',
            'content' => 'required',
            'post_type' => 'required|in_list[setup,pnl,analysis,educational,general]',
            'asset_class' => 'required|in_list[equity,options,futures,crypto,forex,all]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $data = [
            'user_id' => $userId,
            'title' => $this->request->getPost('title'),
            'content' => $this->request->getPost('content'),
            'post_type' => $this->request->getPost('post_type'),
            'asset_class' => $this->request->getPost('asset_class'),
            'tags' => json_encode($this->extractTags($this->request->getPost('content')))
        ];

        // Handle image upload if present
        $image = $this->request->getFile('image');
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Validate image type using config
            if (!$this->uploadConfig->isValidType($image->getMimeType())) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid image type. Only ' . $this->uploadConfig->getAllowedTypesFormatted() . ' are allowed.'
                ]);
            }

            // Validate image size using config
            if (!$this->uploadConfig->isValidSize($image->getSize())) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Image size too large. Maximum ' . $this->uploadConfig->getMaxSizeFormatted() . ' allowed.'
                ]);
            }

            // Use a more robust upload path that works on both local and server
            $uploadResult = $this->handleImageUpload($image);

            if ($uploadResult['success']) {
                // Save only the relative path in database, not the full URL
                $data['image_url'] = $uploadResult['relative_path'];
                log_message('info', 'Image uploaded successfully. Relative path: ' . $data['image_url']);
                log_message('info', 'Full web URL: ' . $uploadResult['url']);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $uploadResult['message']
                ]);
            }
        }

        log_message('info', 'Attempting to insert post with data: ' . json_encode($data));

        if ($this->postModel->insert($data)) {
            $postId = $this->postModel->getInsertID();
            log_message('info', 'Post inserted successfully with ID: ' . $postId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Post created successfully',
                'post_id' => $postId
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create post'
        ]);
    }

    /**
     * Toggle like on post or comment
     */
    public function toggleLike()
    {
        try {
            log_message('info', "=== TOGGLE LIKE REQUEST START ===");
            log_message('info', "Request method: " . $this->request->getMethod());
            log_message('info', "Request URI: " . $this->request->getUri());
            log_message('info', "POST data: " . json_encode($this->request->getPost()));

            // Check if community_likes table exists
            $db = \Config\Database::connect();
            if (!$db->tableExists('community_likes')) {
                log_message('error', 'community_likes table does not exist');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Database table not found. Please run migrations: php spark migrate'
                ]);
            }

            $authCheck = $this->checkAuthentication();
            if ($authCheck !== true) {
                log_message('error', 'Authentication failed for toggleLike');
                return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
            }

            $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

            $likeableType = $this->request->getPost('type'); // 'post' or 'comment'
            $likeableId = $this->request->getPost('id');

            log_message('info', "ToggleLike called - User: $userId, Type: $likeableType, ID: $likeableId");

            if (!in_array($likeableType, ['post', 'comment']) || !$likeableId) {
                log_message('error', 'Invalid parameters for toggleLike');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid parameters'
                ]);
            }

            // Validate that the target post/comment exists
            if ($likeableType === 'post') {
                $targetExists = $this->postModel->find($likeableId);
                if (!$targetExists) {
                    log_message('error', "Post $likeableId not found for like toggle");
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Post not found'
                    ]);
                }
            } elseif ($likeableType === 'comment') {
                $targetExists = $this->commentModel->find($likeableId);
                if (!$targetExists) {
                    log_message('error', "Comment $likeableId not found for like toggle");
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Comment not found'
                    ]);
                }
            }

            $result = $this->likeModel->toggleLike($userId, $likeableType, $likeableId);

            if ($result['action'] === 'error') {
                log_message('error', 'LikeModel toggleLike returned error');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to toggle like'
                ]);
            }

            // Create notification if liked (not unliked)
            if ($result['action'] === 'liked') {
                if ($likeableType === 'post') {
                    $post = $this->postModel->find($likeableId);
                    if ($post && $post['user_id'] != $userId) {
                        $actor = $this->userModel->find($userId);
                        $message = "{$actor['full_name']} liked your post";
                        $this->notificationModel->createNotification(
                            $post['user_id'],
                            $userId,
                            'like_post',
                            'post',
                            $likeableId,
                            $message
                        );
                    }
                } elseif ($likeableType === 'comment') {
                    $comment = $this->commentModel->find($likeableId);
                    if ($comment && $comment['user_id'] != $userId) {
                        $actor = $this->userModel->find($userId);
                        $message = "{$actor['full_name']} liked your comment";
                        $this->notificationModel->createNotification(
                            $comment['user_id'],
                            $userId,
                            'like_comment',
                            'comment',
                            $likeableId,
                            $message
                        );
                    }
                }
            }

            // Get updated like count
            $likeCount = $this->likeModel->getLikeCount($likeableType, $likeableId);

            log_message('info', "ToggleLike successful - Action: {$result['action']}, Count: $likeCount");

            return $this->response->setJSON([
                'success' => true,
                'action' => $result['action'],
                'liked' => $result['liked'],
                'like_count' => $likeCount
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception in toggleLike: ' . $e->getMessage());
            log_message('error', 'Exception trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test endpoint to check if routing works
     */
    public function testLike()
    {
        log_message('info', 'Test like endpoint called');
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Test endpoint working',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Add comment to post
     */
    public function addComment()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $validation = \Config\Services::validation();
        $validation->setRules([
            'post_id' => 'required|integer',
            'content' => 'required|max_length[1000]',
            'parent_id' => 'permit_empty|integer'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $data = [
            'post_id' => $this->request->getPost('post_id'),
            'user_id' => $userId,
            'content' => $this->request->getPost('content'),
            'parent_id' => $this->request->getPost('parent_id') ?: null
        ];

        if ($this->commentModel->insert($data)) {
            $commentId = $this->commentModel->getInsertID();

            // Create notification for post owner or parent comment owner
            $postId = $data['post_id'];
            $parentId = $data['parent_id'];
            $actor = $this->userModel->find($userId);

            if ($parentId) {
                // Reply to comment - notify parent comment owner
                $parentComment = $this->commentModel->find($parentId);
                if ($parentComment && $parentComment['user_id'] != $userId) {
                    $message = "{$actor['full_name']} replied to your comment";
                    $this->notificationModel->createNotification(
                        $parentComment['user_id'],
                        $userId,
                        'reply_comment',
                        'comment',
                        $commentId,
                        $message
                    );
                }
            } else {
                // Comment on post - notify post owner
                $post = $this->postModel->find($postId);
                if ($post && $post['user_id'] != $userId) {
                    $message = "{$actor['full_name']} commented on your post";
                    $this->notificationModel->createNotification(
                        $post['user_id'],
                        $userId,
                        'comment_post',
                        'post',
                        $postId,
                        $message
                    );
                }
            }

            // Get the comment with user info
            $comment = $this->commentModel
                ->select('community_comments.*, users.full_name, users.profile, users.badge')
                ->join('users', 'users.id = community_comments.user_id')
                ->find($commentId);

            $comment['time_ago'] = $this->timeAgo($comment['created_at']);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Comment added successfully',
                'comment' => $comment
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to add comment'
        ]);
    }

    /**
     * Get comments for a post
     */
    public function getComments($postId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        
        $comments = $this->commentModel->getPostComments($postId);
        
        // Add time ago and like status
        foreach ($comments as &$comment) {
            $comment['time_ago'] = $this->timeAgo($comment['created_at']);
            $comment['is_liked'] = $this->likeModel->hasUserLiked($userId, 'comment', $comment['id']);
            
            // Process replies
            if (isset($comment['replies'])) {
                foreach ($comment['replies'] as &$reply) {
                    $reply['time_ago'] = $this->timeAgo($reply['created_at']);
                    $reply['is_liked'] = $this->likeModel->hasUserLiked($userId, 'comment', $reply['id']);
                }
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'comments' => $comments
        ]);
    }

    /**
     * Toggle follow user
     */
    public function toggleFollow()
    {
        try {
            log_message('info', "=== TOGGLE FOLLOW REQUEST START ===");
            log_message('info', "Request method: " . $this->request->getMethod());
            log_message('info', "Request URI: " . $this->request->getUri());
            log_message('info', "POST data: " . json_encode($this->request->getPost()));

            $authCheck = $this->checkAuthentication();
            if ($authCheck !== true) {
                log_message('error', 'Authentication failed for toggleFollow');
                return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
            }

            $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
            $followingId = $this->request->getPost('user_id');

            log_message('info', "ToggleFollow called - User: $userId, Following: $followingId");

            if (!$followingId) {
                log_message('error', 'Missing user_id parameter for toggleFollow');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User ID is required'
                ]);
            }

            // Validate that the target user exists
            $targetUser = $this->userModel->find($followingId);
            if (!$targetUser) {
                log_message('error', "Target user $followingId not found for follow toggle");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            $result = $this->followModel->toggleFollow($userId, $followingId);

            if ($result['action'] === 'error') {
                log_message('error', 'FollowModel toggleFollow returned error: ' . ($result['message'] ?? 'Unknown error'));
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $result['message'] ?? 'Failed to toggle follow'
                ]);
            }

            // Create notification if followed (not unfollowed)
            if ($result['action'] === 'followed') {
                $actor = $this->userModel->find($userId);
                $message = "{$actor['full_name']} started following you";
                $this->notificationModel->createNotification(
                    $followingId,
                    $userId,
                    'follow_user',
                    'user',
                    $userId,
                    $message
                );
            }

            log_message('info', "ToggleFollow successful - Action: {$result['action']}, Following: " . ($result['following'] ? 'true' : 'false'));

            return $this->response->setJSON([
                'success' => true,
                'action' => $result['action'],
                'following' => $result['following']
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception in toggleFollow: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while processing your request'
            ]);
        }
    }

    /**
     * Get user's own posts
     */
    public function getMyPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $posts = $this->postModel->getUserPosts($userId, $limit, $offset);

        // Add user info and format
        foreach ($posts as &$post) {
            $user = $this->userModel->find($userId);
            $post['full_name'] = $user['full_name'];
            $post['profile'] = $user['profile'];
            $post['badge'] = $user['badge'];

            // Convert relative image path to full URL for display
            if (!empty($post['image_url'])) {
                $post['image_url'] = $this->uploadConfig->convertToFullUrl($post['image_url']);
                log_message('debug', "My post {$post['id']} image URL: {$post['image_url']}");
            }

            $post['time_ago'] = $this->timeAgo($post['created_at']);
            $post['is_own_post'] = true;
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Delete user's own post
     */
    public function deletePost($postId)
    {
        try {
            log_message('info', "=== DELETE POST REQUEST START ===");
            log_message('info', "DeletePost called for post ID: $postId");
            log_message('info', "Request method: " . $this->request->getMethod());
            log_message('info', "Request URI: " . $this->request->getUri());

            $authCheck = $this->checkAuthentication();
            if ($authCheck !== true) {
                log_message('error', 'Unauthorized delete attempt');
                return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
            }

            $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
            log_message('info', "Delete request from user: $userId");

            $post = $this->postModel->find($postId);

            if (!$post) {
                log_message('error', "Post not found: $postId");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Post not found'
                ]);
            }

            log_message('info', "Post found - Owner: {$post['user_id']}, Requester: $userId");

            if ($post['user_id'] != $userId) {
                log_message('error', "User $userId attempted to delete post owned by {$post['user_id']}");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'You can only delete your own posts'
                ]);
            }

            // Delete associated image if exists
            if (!empty($post['image_url'])) {
                $this->deletePostImage($post['image_url']);
            }

            // Use permanent delete instead of soft delete
            if ($this->postModel->permanentDelete($postId)) {
                log_message('info', "Post $postId permanently deleted from database");
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Post deleted successfully'
                ]);
            }

            log_message('error', "Failed to delete post $postId from database");
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete post'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception in deletePost: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Internal server error'
            ]);
        }
    }

    /**
     * Search posts
     */
    public function searchPosts()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $query = $this->request->getGet('q');

        if (empty($query) || strlen($query) < 3) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Search query must be at least 3 characters'
            ]);
        }

        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $posts = $this->postModel->searchPosts($query, $limit, $offset);

        // Get like and follow status
        $likeItems = [];
        $authorIds = [];
        foreach ($posts as $post) {
            $likeItems[] = ['type' => 'post', 'id' => $post['id']];
            $authorIds[] = $post['user_id'];
        }

        $likedItems = $this->likeModel->getLikeStatusForItems($userId, $likeItems);
        $followingUsers = $this->followModel->getFollowStatusForUsers($userId, $authorIds);

        foreach ($posts as &$post) {
            $post['is_liked'] = isset($likedItems['post_' . $post['id']]);
            $post['is_following'] = isset($followingUsers[$post['user_id']]);
            $post['is_own_post'] = $post['user_id'] == $userId;
            $post['time_ago'] = $this->timeAgo($post['created_at']);
            $post['parsed_tags'] = $this->parseTags($post['tags']);
        }

        return $this->response->setJSON([
            'success' => true,
            'posts' => $posts,
            'has_more' => count($posts) == $limit
        ]);
    }

    /**
     * Helper function to calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . 'm ago';
        if ($time < 86400) return floor($time/3600) . 'h ago';
        if ($time < 2592000) return floor($time/86400) . 'd ago';
        if ($time < 31536000) return floor($time/2592000) . 'mo ago';
        return floor($time/31536000) . 'y ago';
    }

    /**
     * Extract hashtags and mentions from content
     */
    private function extractTags($content)
    {
        $tags = [];

        // Extract hashtags
        preg_match_all('/#([a-zA-Z0-9_]+)/', $content, $hashtags);
        if (!empty($hashtags[1])) {
            foreach ($hashtags[1] as $tag) {
                $tags[] = ['type' => 'hashtag', 'value' => $tag];
            }
        }

        // Extract mentions
        preg_match_all('/@([a-zA-Z0-9_]+)/', $content, $mentions);
        if (!empty($mentions[1])) {
            foreach ($mentions[1] as $mention) {
                $tags[] = ['type' => 'mention', 'value' => $mention];
            }
        }

        // Extract stock symbols
        preg_match_all('/\$([a-zA-Z0-9_]+)/', $content, $stocks);
        if (!empty($stocks[1])) {
            foreach ($stocks[1] as $stock) {
                $tags[] = ['type' => 'stock', 'value' => $stock];
            }
        }

        return $tags;
    }

    /**
     * Parse tags for display
     */
    private function parseTags($tagsJson)
    {
        if (empty($tagsJson)) {
            return [];
        }

        $tags = json_decode($tagsJson, true);
        return is_array($tags) ? $tags : [];
    }

    /**
     * Get user notifications
     */
    public function getNotifications()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $page = $this->request->getGet('page') ?? 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;

        $notifications = $this->notificationModel->getUserNotifications($userId, $limit, $offset);
        $unreadCount = $this->notificationModel->getUnreadCount($userId);

        return $this->response->setJSON([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => $unreadCount,
            'has_more' => count($notifications) == $limit
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationRead($notificationId)
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if ($this->notificationModel->markAsRead($notificationId, $userId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to mark notification as read'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsRead()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if ($this->notificationModel->markAllAsRead($userId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'All notifications marked as read'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to mark notifications as read'
        ]);
    }

    /**
     * Get unread notification count for sidebar
     */
    public function getUnreadNotificationCount()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));
        $count = $this->notificationModel->getUnreadCount($userId);

        return $this->response->setJSON([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Test upload system - for debugging
     */
    public function testUpload()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        // Test upload paths
        $uploadPaths = $this->uploadConfig->getUploadPaths();
        $results = [];

        foreach ($uploadPaths as $type => $path) {
            $results[$type] = [
                'path' => $path,
                'exists' => is_dir($path),
                'writable' => is_dir($path) ? is_writable($path) : false,
                'can_create' => !is_dir($path) ? is_writable(dirname($path)) : true
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'upload_paths' => $results,
            'base_url' => base_url(),
            'fcpath' => FCPATH,
            'writepath' => WRITEPATH
        ]);
    }

    /**
     * Serve images from writable directory
     */
    public function serveImage($filename)
    {
        // Log the request for debugging
        log_message('info', "Image request: $filename");

        // Validate filename to prevent directory traversal
        if (!preg_match('/^[a-zA-Z0-9_\-\.]+\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
            log_message('warning', "Invalid filename format: $filename");
            return $this->response->setStatusCode(404, 'File not found');
        }

        // Get the file path - try multiple possible locations
        $possiblePaths = [
            WRITEPATH . 'uploads/community/' . $filename,
            FCPATH . 'uploads/community/' . $filename,
            ROOTPATH . 'public/uploads/community/' . $filename,
            ROOTPATH . 'writable/uploads/community/' . $filename
        ];

        $filePath = null;
        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                $filePath = $path;
                log_message('info', "Found image at: $path");
                break;
            }
        }

        // Check if file exists
        if (!$filePath) {
            log_message('error', "Image not found in any location: $filename");
            log_message('error', "Searched paths: " . implode(', ', $possiblePaths));
            return $this->response->setStatusCode(404, 'File not found');
        }

        // Get file info
        $fileInfo = pathinfo($filePath);
        $mimeType = $this->getMimeType($fileInfo['extension']);

        log_message('info', "Serving image: $filePath (MIME: $mimeType, Size: " . filesize($filePath) . " bytes)");

        // Set appropriate headers
        $this->response->setHeader('Content-Type', $mimeType);
        $this->response->setHeader('Content-Length', filesize($filePath));
        $this->response->setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
        $this->response->setHeader('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        $this->response->setHeader('Access-Control-Allow-Origin', '*'); // Allow cross-origin requests

        // Output the file
        $this->response->setBody(file_get_contents($filePath));
        return $this->response;
    }

    /**
     * Get MIME type for image files
     */
    private function getMimeType($extension)
    {
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];

        return $mimeTypes[strtolower($extension)] ?? 'application/octet-stream';
    }

    /**
     * Fix existing image URLs in database - convert full URLs to relative paths
     * Call this once to clean up existing data: /community/fixImageUrls
     */
    public function fixImageUrls()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $this->response->setJSON(['success' => false, 'message' => 'Unauthorized']);
        }

        try {
            $db = \Config\Database::connect();

            // Get all posts with image URLs
            $builder = $db->table('community_posts');
            $builder->select('id, image_url');
            $builder->where('image_url IS NOT NULL');
            $builder->where('image_url !=', '');

            $posts = $builder->get()->getResultArray();
            $updated = 0;
            $errors = [];

            if (!empty($posts)) {
                foreach ($posts as $post) {
                    $originalUrl = $post['image_url'];
                    $newUrl = $this->uploadConfig->convertToRelativePath($originalUrl);

                    if ($newUrl !== $originalUrl) {
                        $updateBuilder = $db->table('community_posts');
                        $updateBuilder->where('id', $post['id']);

                        if ($updateBuilder->update(['image_url' => $newUrl])) {
                            $updated++;
                            log_message('info', "Updated post {$post['id']}: '$originalUrl' -> '$newUrl'");
                        } else {
                            $errors[] = "Failed to update post {$post['id']}";
                        }
                    }
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Processed " . count($posts) . " posts, updated $updated URLs",
                'total_posts' => count($posts),
                'updated_count' => $updated,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fixing image URLs: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fixing image URLs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Debug endpoint to check image URLs and file existence
     */
    public function debugImages()
    {
        try {
            $db = \Config\Database::connect();

            // Get recent posts with images
            $builder = $db->table('community_posts');
            $builder->select('id, title, image_url, created_at');
            $builder->where('image_url IS NOT NULL');
            $builder->where('image_url !=', '');
            $builder->orderBy('created_at', 'DESC');
            $builder->limit(10);

            $posts = $builder->get()->getResultArray();
            $debug_info = [];

            foreach ($posts as $post) {
                $filename = basename($post['image_url']);
                $possiblePaths = [
                    WRITEPATH . 'uploads/community/' . $filename,
                    FCPATH . 'uploads/community/' . $filename,
                    ROOTPATH . 'public/uploads/community/' . $filename,
                    ROOTPATH . 'writable/uploads/community/' . $filename
                ];

                $fileExists = [];
                foreach ($possiblePaths as $path) {
                    $fileExists[$path] = file_exists($path);
                }

                $fullUrl = $this->uploadConfig->convertToFullUrl($post['image_url']);

                $debug_info[] = [
                    'post_id' => $post['id'],
                    'title' => $post['title'],
                    'stored_url' => $post['image_url'],
                    'converted_url' => $fullUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'base_url' => base_url(),
                    'writepath' => WRITEPATH,
                    'fcpath' => FCPATH,
                    'rootpath' => ROOTPATH
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'debug_info' => $debug_info,
                'server_info' => [
                    'php_version' => PHP_VERSION,
                    'ci_version' => \CodeIgniter\CodeIgniter::CI_VERSION,
                    'environment' => ENVIRONMENT,
                    'base_url' => base_url(),
                    'writepath' => WRITEPATH,
                    'fcpath' => FCPATH,
                    'rootpath' => ROOTPATH
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete post image from multiple possible locations
     */
    private function deletePostImage($imageUrl)
    {
        try {
            // Handle both relative paths and full URLs
            if (str_starts_with($imageUrl, 'http')) {
                // Extract filename from full URL
                $filename = basename($imageUrl);
            } else {
                // It's a relative path, extract filename
                $filename = basename($imageUrl);
            }

            // Get possible image locations from config
            $uploadPaths = $this->uploadConfig->getUploadPaths();
            $imagePaths = [];

            foreach ($uploadPaths as $path) {
                $imagePaths[] = $path . $filename;
            }

            $deleted = false;
            foreach ($imagePaths as $imagePath) {
                if (file_exists($imagePath)) {
                    if (unlink($imagePath)) {
                        log_message('info', "Deleted image: $imagePath");
                        $deleted = true;
                    } else {
                        log_message('warning', "Failed to delete image: $imagePath");
                    }
                }
            }

            if (!$deleted) {
                log_message('warning', "Image not found for deletion: $imageUrl");
            }

        } catch (\Exception $e) {
            log_message('error', 'Error deleting image: ' . $e->getMessage());
        }
    }

    /**
     * Handle image upload with robust path handling for both local and server environments
     */
    private function handleImageUpload($image)
    {
        try {
            // Generate unique filename
            $newName = $image->getRandomName();

            // Get upload paths from config
            $uploadPaths = $this->uploadConfig->getUploadPaths();

            $uploadPath = null;
            $webPath = null;

            // Try each path until we find one that works
            foreach ($uploadPaths as $type => $path) {
                // Ensure directory exists
                if (!is_dir($path)) {
                    if (!mkdir($path, 0755, true)) {
                        log_message('warning', "Could not create directory: $path");
                        continue;
                    }
                    log_message('info', "Created directory: $path");
                }

                // Check if directory is writable
                if (!is_writable($path)) {
                    log_message('warning', "Directory not writable: $path");
                    continue;
                }

                // Set the upload path and corresponding web path
                $uploadPath = $path;
                $webPath = $this->uploadConfig->getWebUrl($newName);
                $relativePath = $this->uploadConfig->getRelativePath($newName);

                log_message('info', "Using upload path ($type): $uploadPath");
                break;
            }

            if (!$uploadPath) {
                return [
                    'success' => false,
                    'message' => 'No writable upload directory found. Please check server permissions.'
                ];
            }

            // Attempt to move the uploaded file
            if ($image->move($uploadPath, $newName)) {
                // Verify the file was actually moved
                $finalPath = $uploadPath . $newName;
                if (!file_exists($finalPath)) {
                    log_message('error', "File move reported success but file doesn't exist: $finalPath");
                    return [
                        'success' => false,
                        'message' => 'File upload failed - file not found after move'
                    ];
                }

                // Also copy to public directory as fallback for server environments
                $publicPath = FCPATH . 'uploads/community/';
                if (!is_dir($publicPath)) {
                    mkdir($publicPath, 0755, true);
                }

                $publicFilePath = $publicPath . $newName;
                if (copy($finalPath, $publicFilePath)) {
                    log_message('info', "Image also copied to public directory: $publicFilePath");
                } else {
                    log_message('warning', "Failed to copy image to public directory: $publicFilePath");
                }

                log_message('info', "Image will be served through controller route: $webPath");

                // Log the final URLs for debugging
                log_message('info', "Image upload successful:");
                log_message('info', "  - File path: $finalPath");
                log_message('info', "  - Web URL: $webPath");
                log_message('info', "  - Relative path: $relativePath");
                log_message('info', "  - File size: " . filesize($finalPath) . " bytes");

                return [
                    'success' => true,
                    'url' => $webPath,
                    'relative_path' => $relativePath,
                    'path' => $finalPath
                ];
            } else {
                log_message('error', "Failed to move uploaded file from temp to: $uploadPath$newName");
                return [
                    'success' => false,
                    'message' => 'Failed to move uploaded file. Please check server permissions.'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Image upload error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Fix existing posts with wrong image URLs (one-time fix)
     */
    private function fixExistingImageUrls()
    {
        try {
            // Check if we've already run this fix
            $cacheKey = 'image_urls_fixed';
            if (cache($cacheKey)) {
                return; // Already fixed
            }

            // Get all posts with image URLs containing 'writable/uploads'
            $db = \Config\Database::connect();
            $builder = $db->table('community_posts');
            $builder->select('id, image_url');
            $builder->where('image_url IS NOT NULL');
            $builder->like('image_url', 'writable/uploads/community');

            $postsToFix = $builder->get()->getResultArray();

            if (!empty($postsToFix)) {
                log_message('info', 'Found ' . count($postsToFix) . ' posts with incorrect image URLs');

                foreach ($postsToFix as $post) {
                    $newUrl = str_replace('writable/uploads/community', 'uploads/community', $post['image_url']);

                    $builder = $db->table('community_posts');
                    $builder->where('id', $post['id']);
                    $builder->update(['image_url' => $newUrl]);

                    log_message('info', "Fixed image URL for post {$post['id']}: {$newUrl}");
                }

                log_message('info', 'Successfully fixed all image URLs');
            }

            // Mark as fixed so we don't run this again
            cache()->save($cacheKey, true, 86400 * 30); // Cache for 30 days

        } catch (\Exception $e) {
            log_message('error', 'Error fixing image URLs: ' . $e->getMessage());
        }
    }

    /**
     * Create likes table if it doesn't exist
     */
    private function createLikesTable()
    {
        try {
            $forge = \Config\Database::forge();

            $forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true,
                ],
                'user_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'comment' => 'User who liked'
                ],
                'likeable_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['post', 'comment'],
                    'comment' => 'Type of item being liked'
                ],
                'likeable_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'comment' => 'ID of the item being liked'
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                ],
            ]);

            $forge->addKey('id', true);
            $forge->addKey('user_id');
            $forge->addKey(['likeable_type', 'likeable_id']);
            $forge->addUniqueKey(['user_id', 'likeable_type', 'likeable_id'], 'unique_like');

            if ($forge->createTable('community_likes')) {
                log_message('info', 'Successfully created community_likes table');
            } else {
                log_message('error', 'Failed to create community_likes table');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating likes table: ' . $e->getMessage());
        }
    }
}
