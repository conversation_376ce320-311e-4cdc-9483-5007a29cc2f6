<!-- Community Feed Styles -->
<style>
    :root {
        --primary: #4fd1c5;
        --primary-dark: #319795;
        --secondary: #805ad5;
        --secondary-dark: #6b46c1;
        --accent: #f687b3;
        --accent-dark: #e53e3e;
        --dark: #1a202c;
        --darker: #171923;
        --light: #f7fafc;
        --gray: #e2e8f0;
        --dark-gray: #2d3748;
    }

    /* Smooth scrolling for the entire page */
    html {
        scroll-behavior: smooth;
    }

    /* Custom scrollbar styling */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: var(--darker);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: var(--primary);
        border-radius: 4px;
        transition: background 0.3s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }

    /* Smooth scrolling for containers */
    .smooth-scroll {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Enhanced scrolling performance */
    .scroll-container {
        will-change: scroll-position;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    /* Smooth animations for posts */
    .post-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
    }

    .post-card:hover {
        transform: translateY(-2px);
    }

    /* Fade in animation for new posts */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Loading animation */
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    .loading-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    /* Smooth transitions for sidebar */
    .sidebar a {
        transition: all 0.2s ease-in-out;
    }

    /* Smooth modal transitions */
    .modal-backdrop {
        transition: opacity 0.3s ease-in-out;
    }

    .modal-content {
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .glass-card {
        background: rgba(26, 32, 44, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.08);
    }

    .post-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(79, 209, 197, 0.1);
    }

    .glow-button {
        box-shadow: 0 0 15px rgba(79, 209, 197, 0.5);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 15px rgba(79, 209, 197, 0.5); }
        50% { box-shadow: 0 0 25px rgba(79, 209, 197, 0.8); }
        100% { box-shadow: 0 0 15px rgba(79, 209, 197, 0.5); }
    }

    .hashtag { color: var(--primary); }
    .mention { color: var(--accent); }
    .stock-tag { color: var(--secondary); }

    .code-block {
        background-color: #2d3748;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
    }

    .reaction-btn:hover { transform: scale(1.1); }
    .badge-pro { background: linear-gradient(90deg, var(--primary), var(--secondary)); }
    .badge-gainer { background: linear-gradient(90deg, #48bb78, #38b2ac); }
    .badge-master { background: linear-gradient(90deg, var(--accent), #ed8936); }
    .tab-active { border-bottom: 2px solid var(--primary); color: var(--primary); }

    .comment-section {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .comment-section.active {
        max-height: 1000px;
        transition: max-height 0.5s ease-in;
    }

    .comment-input {
        background: rgba(45, 55, 72, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .comment-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .neon-text { text-shadow: 0 0 5px rgba(79, 209, 197, 0.5); }

    .modal {
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
    }

    .modal.active {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        transform: translateY(-20px);
        transition: transform 0.3s ease;
    }

    .modal.active .modal-content { transform: translateY(0); }

    .post-type-btn {
        transition: all 0.2s ease;
    }

    .post-type-btn.active {
        background-color: var(--primary);
        color: white;
    }

    .post-type-btn:hover:not(.active) {
        background-color: rgba(79, 209, 197, 0.1);
    }

    .follow-btn {
        background: linear-gradient(90deg, var(--primary), var(--secondary));
        transition: all 0.3s ease;
    }

    .follow-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(79, 209, 197, 0.3);
    }

    .follow-btn.following {
        background: var(--dark-gray);
        border: 1px solid var(--primary);
    }

    .reply-section {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        margin-left: 2rem;
        border-left: 2px solid var(--primary);
        padding-left: 1rem;
    }

    .reply-section.active { max-height: 200px; }
    .reply-btn { color: var(--primary); font-size: 0.8rem; }
    .reply-btn:hover { text-decoration: underline; }
    .reply-indicator { color: var(--primary); font-size: 0.7rem; margin-left: 0.5rem; }

    .sidebar {
        transition: all 0.3s ease;
        background-color: var(--dark);
    }

    .sidebar-collapsed {
        width: 80px !important;
    }

    .sidebar-collapsed .nav-text {
        display: none;
    }

    .sidebar-collapsed .logo-text {
        display: none;
    }

    .sidebar-collapsed .logo-icon {
        margin: 0 auto;
    }

    /* Navigation active states */
    .nav-item {
        transition: all 0.2s ease-in-out;
    }

    .nav-item.active {
        background-color: #374151 !important;
        color: white !important;
    }

    .nav-item.active .nav-text {
        color: white !important;
    }

    .nav-item:hover {
        background-color: #4B5563;
        color: white;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
    }

    .overlay.active {
        display: block;
    }

    /* Hide main Trade Diary sidebar and topbar on community page for all screen sizes */
    #sidebar {
        display: none !important;
    }

    .topbar {
        display: none !important;
    }

    /* Ensure community page takes full screen */
    .community-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        background: #111827;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        /* Mobile-specific adjustments already handled above */

        /* Community sidebar mobile styles - IMPORTANT: Override desktop styles */
        .community-container .community-sidebar {
            position: fixed !important;
            top: 0 !important;
            left: -100% !important;
            height: 100vh !important;
            width: 85vw !important;
            max-width: 320px !important;
            z-index: 1050 !important;
            transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            background: #1f2937 !important;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.5) !important;
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
            transform: none !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .community-container .community-sidebar.active {
            left: 0 !important;
            transform: none !important;
        }

        /* Ensure overlay works properly */
        .community-container .overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(0, 0, 0, 0.5) !important;
            z-index: 1040 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: all 0.3s ease !important;
            display: block !important;
        }

        .community-container .overlay.active {
            opacity: 1 !important;
            visibility: visible !important;
        }

        .community-container .overlay.hidden {
            opacity: 0 !important;
            visibility: hidden !important;
        }

        /* Main content adjustments */
        .main-content {
            width: 100%;
            padding: 0;
            height: 100vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Mobile header */
        .mobile-header {
            position: sticky;
            top: 0;
            background: #1f2937;
            border-bottom: 1px solid #374151;
            z-index: 1020;
            padding: 1rem;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 1.25rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
            width: 2.5rem;
            height: 2.5rem;
        }

        .mobile-menu-btn:hover {
            color: #ffffff;
            background: #374151;
        }

        /* Feed header mobile layout */
        .feed-header {
            padding: 1rem;
            background: #111827;
            border-bottom: 1px solid #374151;
        }

        .feed-header h1 {
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
        }

        /* Feed filters mobile layout */
        .feed-filters {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            width: 100%;
        }

        .feed-filters-row {
            display: flex;
            gap: 0.5rem;
        }

        .feed-filters select {
            flex: 1;
            font-size: 0.875rem;
            padding: 0.75rem;
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 0.5rem;
            color: white;
        }

        .feed-filters button {
            width: 100%;
            font-size: 0.875rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
        }

        /* Tabs mobile scrolling */
        .tabs {
            overflow-x: auto;
            white-space: nowrap;
            padding: 0 1rem 0.5rem;
            margin: 0 -1rem;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
        }

        .tabs::-webkit-scrollbar {
            display: none;
        }

        .tab-btn {
            flex-shrink: 0;
            margin-right: 0.5rem;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
        }

        /* Post cards mobile layout */
        .post-card {
            margin: 0 1rem 1.5rem;
            padding: 1rem;
        }

        /* Post actions mobile layout */
        .post-actions {
            flex-direction: column;
            gap: 0.75rem;
        }

        .post-actions > div {
            width: 100%;
            justify-content: space-around;
        }

        /* User info mobile layout */
        .user-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .user-badges {
            width: 100%;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        /* Modal mobile styles */
        .modal-content {
            width: 95%;
            max-width: none;
            margin: 1rem auto;
            max-height: 90vh;
            overflow-y: auto;
        }

        /* Comment input mobile layout */
        .comment-input-container {
            flex-direction: column;
            gap: 0.5rem;
        }

        .comment-input-container input {
            border-radius: 0.5rem !important;
            width: 100%;
        }

        .comment-input-container button {
            border-radius: 0.5rem !important;
            width: 100%;
            padding: 0.75rem;
        }

        /* Reply section mobile */
        .reply-section {
            margin-left: 0.5rem;
            padding-left: 0.5rem;
            border-left: 2px solid #374151;
        }

        /* Image preview mobile */
        .image-preview img {
            max-width: 100%;
            height: auto;
        }

        /* Form elements mobile */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            font-size: 1rem;
            padding: 0.75rem;
        }

        /* Post type buttons mobile */
        .post-type-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .post-type-btn {
            font-size: 0.875rem;
            padding: 0.5rem;
            text-align: center;
        }

        /* Notification badge mobile */
        #notificationBadge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* Loading indicator mobile */
        .loading-indicator {
            padding: 2rem 1rem;
        }

        /* Scroll to top button mobile */
        #scrollToTop {
            bottom: 5rem;
            right: 1rem;
            width: 3rem;
            height: 3rem;
        }

        /* FAB mobile adjustments */
        #createPostFAB {
            bottom: 1.5rem;
            right: 1.5rem;
            width: 3.5rem;
            height: 3.5rem;
        }
    }

    /* Tablet responsive styles */
    @media (min-width: 769px) and (max-width: 1024px) {
        .sidebar {
            width: 200px;
        }

        .feed-filters {
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .post-card {
            padding: 1.25rem;
        }

        .modal-content {
            width: 80%;
            max-width: 600px;
        }
    }

    /* Large mobile landscape */
    @media (max-width: 768px) and (orientation: landscape) {
        .feed-header {
            padding: 0.75rem 1rem;
        }

        .feed-header h1 {
            font-size: 1.25rem;
        }

        .modal-content {
            max-height: 85vh;
        }
    }

    /* Small mobile styles */
    @media (max-width: 480px) {
        .feed-filters {
            flex-direction: column;
        }

        .feed-filters select,
        .feed-filters button {
            width: 100%;
        }

        .post-type-buttons {
            grid-template-columns: 1fr;
        }

        .tabs {
            padding: 0 0.5rem 0.5rem;
            margin: 0 -0.5rem;
        }

        .post-card {
            margin: 0 0.5rem 1rem;
            padding: 0.75rem;
        }

        .user-badges {
            flex-direction: column;
            align-items: flex-start;
        }

        .community-sidebar {
            width: 100%;
        }
    }

    /* Desktop styles - community page specific */
    @media (min-width: 769px) {
        .community-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
        }

        .community-container .community-sidebar {
            position: static !important;
            left: 0 !important;
            width: 256px !important;
            max-width: none !important;
            height: auto !important;
            transform: none !important;
            box-shadow: none !important;
        }

        .mobile-header {
            display: none;
        }

        .community-container .overlay {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    }

    /* Floating Action Button (FAB) Styles */
    #createPostFAB {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
        box-shadow: 0 8px 25px rgba(20, 184, 166, 0.3);
        border: none;
        cursor: pointer;
        user-select: none;
        -webkit-tap-highlight-color: transparent;
    }

    #createPostFAB:hover {
        background: linear-gradient(135deg, #0d9488, #0f766e);
        box-shadow: 0 12px 35px rgba(20, 184, 166, 0.4);
        transform: translateY(-2px);
    }

    #createPostFAB:active {
        transform: translateY(0);
        box-shadow: 0 6px 20px rgba(20, 184, 166, 0.3);
    }

    /* FAB Animation */
    @keyframes fabPulse {
        0% {
            box-shadow: 0 8px 25px rgba(20, 184, 166, 0.3);
        }
        50% {
            box-shadow: 0 8px 25px rgba(20, 184, 166, 0.5);
        }
        100% {
            box-shadow: 0 8px 25px rgba(20, 184, 166, 0.3);
        }
    }

    #createPostFAB:focus {
        outline: none;
        animation: fabPulse 2s infinite;
    }

    /* Image Display Optimization */
    .post-image {
        max-width: 400px;
        max-height: 300px;
        object-fit: cover;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .post-image:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    /* Image Zoom Modal */
    .image-zoom-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.9);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .image-zoom-modal.active {
        opacity: 1;
        visibility: visible;
    }

    .image-zoom-modal img {
        max-width: 90vw;
        max-height: 90vh;
        object-fit: contain;
        border-radius: 0.5rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    }

    .image-zoom-close {
        position: absolute;
        top: 2rem;
        right: 2rem;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .image-zoom-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    /* Mobile image optimizations */
    @media (max-width: 768px) {
        .post-image {
            max-width: 100%;
            max-height: 250px;
        }

        .image-zoom-close {
            top: 1rem;
            right: 1rem;
            width: 2.5rem;
            height: 2.5rem;
        }



        /* Prevent text selection during swipe gestures */
        .main-content {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
        }

        /* Re-enable text selection for content areas */
        .post-content, .comment-content, input, textarea {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            -webkit-touch-callout: default;
        }
    }



    .overlay {
        transition: opacity 0.3s ease;
    }

    /* Improved touch targets for mobile */
    @media (max-width: 768px) {
        .tab-btn {
            min-height: 44px;
            padding: 0.75rem 1rem;
        }

        .mobile-menu-btn {
            min-height: 44px;
            min-width: 44px;
        }

        .reaction-btn {
            min-height: 44px;
            padding: 0.5rem;
        }

        .nav-text {
            font-size: 1rem;
        }
    }
</style>

<!-- Community Page Container -->
<div class="community-container">
    <div class="flex h-screen overflow-hidden bg-gray-900">
        <!-- Community Sidebar -->
        <div class="community-sidebar sidebar w-64 flex-shrink-0 flex flex-col bg-gray-800 border-r border-gray-700 transition-all duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <div class="flex items-center space-x-2">
                    <div class="logo-icon">
                        <i class="fas fa-users text-2xl text-teal-400"></i>
                    </div>
                    <span class="logo-text text-xl font-bold text-teal-400 neon-text">COMMUNITY</span>
                </div>
                <button id="sidebarToggle" class="hidden md:block text-gray-400 hover:text-white focus:outline-none transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <!-- Mobile close button -->
                <button id="mobileSidebarClose" class="md:hidden text-gray-400 hover:text-white focus:outline-none transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="flex-1 overflow-y-auto py-4 smooth-scroll scroll-container">
                <nav>
                    <ul class="space-y-2 px-2">
                        <li>
                            <a href="#" id="nav-home-feed" onclick="navigateToSection('home-feed'); return false;" class="nav-item flex items-center p-3 rounded-lg text-white bg-gray-700 hover:bg-gray-600 transition-colors active">
                                <i class="fas fa-home text-teal-400 w-6"></i>
                                <span class="nav-text ml-3">Home Feed</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-my-posts" onclick="navigateToSection('my-posts'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-pen text-blue-400 w-6"></i>
                                <span class="nav-text ml-3">My Posts</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-setups" onclick="navigateToSection('setups'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-lightbulb text-purple-400 w-6"></i>
                                <span class="nav-text ml-3">Setups & Ideas</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-pnl" onclick="navigateToSection('pnl'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-chart-line text-green-400 w-6"></i>
                                <span class="nav-text ml-3">P&L Shares</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-analysis" onclick="navigateToSection('analysis'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-search-dollar text-yellow-400 w-6"></i>
                                <span class="nav-text ml-3">Market Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-watchlist" onclick="navigateToSection('watchlist'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-binoculars text-red-400 w-6"></i>
                                <span class="nav-text ml-3">Watchlist</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" id="nav-notifications" onclick="navigateToSection('notifications'); return false;" class="nav-item flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                <i class="fas fa-bell text-indigo-400 w-6"></i>
                                <span class="nav-text ml-3">Notifications</span>
                                <span id="notificationBadge" class="ml-auto bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full" style="display: none;">0</span>
                            </a>
                        </li>
                </ul>

                    <div class="border-t border-gray-700 mt-4 pt-4 px-2">
                        <ul class="space-y-2">
                            <li>
                                <a href="<?= base_url('profile') ?>" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                    <i class="fas fa-user text-cyan-400 w-6"></i>
                                    <span class="nav-text ml-3">Profile</span>
                                </a>
                            </li>
                            <li>
                                <a href="<?= base_url('dashboard') ?>" class="flex items-center p-3 rounded-lg text-gray-300 hover:bg-gray-700 hover:text-white transition-colors">
                                    <i class="fas fa-arrow-left text-gray-400 w-6"></i>
                                    <span class="nav-text ml-3">Back to Dashboard</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>

            <div class="p-4 border-t border-gray-700">
                <div class="flex items-center">
                    <img src="<?= $userDetails['profile'] ?? 'https://randomuser.me/api/portraits/men/32.jpg' ?>" alt="User" class="w-10 h-10 rounded-full border-2 border-teal-400">
                    <div class="ml-3">
                        <div class="text-sm font-medium text-white"><?= $userDetails['full_name'] ?? 'TraderX' ?></div>
                        <div class="text-xs text-gray-400">Community Member</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Action Button (FAB) -->
        <button id="createPostFAB" class="fixed bottom-6 right-6 w-14 h-14 bg-teal-500 hover:bg-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 flex items-center justify-center group">
            <i class="fas fa-plus text-xl group-hover:rotate-90 transition-transform duration-300"></i>
        </button>

        <!-- Overlay for mobile -->
        <div class="overlay bg-black bg-opacity-50 fixed inset-0 z-1040 hidden"></div>

        <!-- Image Zoom Modal -->
        <div id="imageZoomModal" class="image-zoom-modal">
            <button class="image-zoom-close" onclick="closeImageZoom()">
                <i class="fas fa-times text-xl"></i>
            </button>
            <img id="zoomedImage" src="" alt="Zoomed image">
        </div>

        <!-- Main Community Content -->
        <div class="flex-1 overflow-y-auto bg-gray-900 smooth-scroll scroll-container main-content">
            <!-- Mobile header with menu button -->
            <div class="md:hidden mobile-header">
                <div class="flex items-center justify-between">
                    <button id="mobileSidebarToggle" class="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-lg font-bold text-white">Community</h1>
                    <div class="w-10"></div> <!-- Spacer for centering -->
                </div>
            </div>

            <!-- Main feed -->
            <div class="p-0 md:p-4">
                <!-- Feed header with filters (Desktop) -->
                <div class="hidden md:flex items-center justify-between mb-6 feed-header">
                    <h1 class="text-2xl font-bold text-white neon-text">COMMUNITY FEED</h1>
                    <div class="flex space-x-2 feed-filters">
                        <div class="relative">
                            <select id="assetFilter" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option value="all">All Assets</option>
                                <option value="equity">Equity</option>
                                <option value="options">Options</option>
                                <option value="futures">Futures</option>
                                <option value="crypto">Crypto</option>
                                <option value="forex">Forex</option>
                            </select>
                        </div>
                        <div class="relative">
                            <select id="sortFilter" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2 pr-8">
                                <option value="latest">Latest</option>
                                <option value="trending">Trending</option>
                                <option value="most_liked">Most Liked</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Mobile filters -->
                <div class="md:hidden feed-header">
                    <div class="feed-filters">
                        <div class="feed-filters-row">
                            <select id="assetFilterMobile" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500">
                                <option value="all">All Assets</option>
                                <option value="equity">Equity</option>
                                <option value="options">Options</option>
                                <option value="futures">Futures</option>
                                <option value="crypto">Crypto</option>
                                <option value="forex">Forex</option>
                            </select>
                            <select id="sortFilterMobile" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500">
                                <option value="latest">Latest</option>
                                <option value="trending">Trending</option>
                                <option value="most_liked">Most Liked</option>
                            </select>
                        </div>
                    </div>
                </div>

        <!-- Tabs -->
        <div class="flex border-b border-gray-800 mb-6 tabs">
            <button class="tab-btn tab-active px-4 py-2 font-medium text-white" data-tab="all">All</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="setup">Setups</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="pnl">P&L</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="analysis">Analysis</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="educational">Educational</button>
            <button class="tab-btn px-4 py-2 font-medium text-gray-400 hover:text-white" data-tab="my-posts">My Posts</button>
        </div>

        <!-- Posts Container -->
        <div class="space-y-6" id="postsContainer">
            <!-- Posts will be loaded here via AJAX -->
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-400 mx-auto"></div>
                <p class="text-gray-400 mt-4">Loading posts...</p>
            </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-8">
            <button id="loadMoreBtn" class="bg-gray-800 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium" style="display: none;">
                Load More Posts
            </button>
        </div>
    </div>
</div>

<script>
// Set base URL for JavaScript
const base_url = '<?= base_url() ?>';

// Community page optimization - hide main sidebar for all screen sizes
document.addEventListener('DOMContentLoaded', function() {
    // Hide main sidebar and topbar for community page (all screen sizes)
    function hideMainElements() {
        const mainSidebar = document.getElementById('sidebar');
        const topbar = document.querySelector('.topbar');

        if (mainSidebar) {
            mainSidebar.style.display = 'none';
        }
        if (topbar) {
            topbar.style.display = 'none';
        }
    }

    // Community sidebar functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const communitySidebar = document.querySelector('.community-sidebar');
    const overlay = document.querySelector('.overlay');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');
    const mobileSidebarClose = document.getElementById('mobileSidebarClose');

    // Debug: Check if elements are found
    console.log('Debug - Elements found:');
    console.log('sidebarToggle:', sidebarToggle);
    console.log('communitySidebar:', communitySidebar);
    console.log('overlay:', overlay);
    console.log('mobileSidebarToggle:', mobileSidebarToggle);
    console.log('mobileSidebarClose:', mobileSidebarClose);

    // Desktop sidebar toggle
    if (sidebarToggle && communitySidebar) {
        sidebarToggle.addEventListener('click', function() {
            communitySidebar.classList.toggle('sidebar-collapsed');
            localStorage.setItem('communitySidebarCollapsed', communitySidebar.classList.contains('sidebar-collapsed'));
        });
    }

    // Restore desktop sidebar state
    if (localStorage.getItem('communitySidebarCollapsed') === 'true' && communitySidebar) {
        communitySidebar.classList.add('sidebar-collapsed');
    }

    // Mobile sidebar toggle
    if (mobileSidebarToggle && communitySidebar && overlay) {
        mobileSidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Mobile sidebar toggle clicked');

            communitySidebar.classList.add('active');
            overlay.classList.remove('hidden');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling

            console.log('Sidebar should be visible now');
        });
    }

    // Mobile sidebar close
    if (mobileSidebarClose && communitySidebar && overlay) {
        mobileSidebarClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Mobile sidebar close clicked');

            communitySidebar.classList.remove('active');
            overlay.classList.add('hidden');
            overlay.classList.remove('active');
            document.body.style.overflow = ''; // Restore scrolling
        });
    }

    // Close sidebar when clicking overlay
    if (overlay && communitySidebar) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                console.log('Overlay clicked');
                communitySidebar.classList.remove('active');
                overlay.classList.add('hidden');
                overlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }
        });
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && communitySidebar && overlay) {
            if (communitySidebar.classList.contains('active')) {
                console.log('Escape key pressed - closing sidebar');
                communitySidebar.classList.remove('active');
                overlay.classList.add('hidden');
                overlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }
        }
    });

    // Floating Action Button (FAB) handler
    const createPostFAB = document.getElementById('createPostFAB');

    function openCreatePostModal() {
        const modal = document.getElementById('createPostModal');
        if (modal) {
            modal.classList.add('active');
        }
    }

    if (createPostFAB) {
        createPostFAB.addEventListener('click', openCreatePostModal);
    }

    // Sync mobile filters with desktop filters
    const assetFilter = document.getElementById('assetFilter');
    const assetFilterMobile = document.getElementById('assetFilterMobile');
    const sortFilter = document.getElementById('sortFilter');
    const sortFilterMobile = document.getElementById('sortFilterMobile');

    if (assetFilter && assetFilterMobile) {
        assetFilter.addEventListener('change', function() {
            assetFilterMobile.value = this.value;
        });
        assetFilterMobile.addEventListener('change', function() {
            assetFilter.value = this.value;
            // Trigger change event on desktop filter
            assetFilter.dispatchEvent(new Event('change'));
        });
    }

    if (sortFilter && sortFilterMobile) {
        sortFilter.addEventListener('change', function() {
            sortFilterMobile.value = this.value;
        });
        sortFilterMobile.addEventListener('change', function() {
            sortFilter.value = this.value;
            // Trigger change event on desktop filter
            sortFilter.dispatchEvent(new Event('change'));
        });
    }

    // Initialize optimizations
    hideMainElements();

    // Handle window resize
    window.addEventListener('resize', function() {
        hideMainElements();

        // Close mobile sidebar on desktop
        if (window.innerWidth > 768 && communitySidebar && overlay) {
            if (communitySidebar.classList.contains('active')) {
                console.log('Closing sidebar due to window resize');
                communitySidebar.classList.remove('active');
                overlay.classList.add('hidden');
                overlay.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            }
        }
    });

    // Improve mobile scroll performance
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.style.webkitOverflowScrolling = 'touch';
        mainContent.style.overflowScrolling = 'touch';
    }

    // Improve mobile sidebar scroll performance
    if (communitySidebar) {
        communitySidebar.style.webkitOverflowScrolling = 'touch';
        communitySidebar.style.overflowScrolling = 'touch';
    }

    // Add touch event support for better mobile interaction
    let touchStartX = 0;
    let touchStartY = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    }, { passive: true });

    document.addEventListener('touchmove', function(e) {
        if (!communitySidebar || !overlay) return;

        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;
        const deltaX = touchX - touchStartX;
        const deltaY = touchY - touchStartY;

        // Swipe right to open sidebar (only if sidebar is closed and swipe starts from left edge)
        if (touchStartX < 20 && deltaX > 50 && Math.abs(deltaY) < 100 && !communitySidebar.classList.contains('active')) {
            communitySidebar.classList.add('active');
            overlay.classList.remove('hidden');
            overlay.classList.add('active');
            overlay.classList.add('block');
            document.body.style.overflow = 'hidden';
        }

        // Swipe left to close sidebar (only if sidebar is open)
        if (communitySidebar.classList.contains('active') && deltaX < -50 && Math.abs(deltaY) < 100) {
            communitySidebar.classList.remove('active');
            overlay.classList.add('hidden');
            overlay.classList.remove('active');
            overlay.classList.remove('block');
            document.body.style.overflow = '';
        }
    }, { passive: true });
});

// Image Zoom Functionality
function openImageZoom(imageSrc) {
    const modal = document.getElementById('imageZoomModal');
    const zoomedImage = document.getElementById('zoomedImage');

    if (modal && zoomedImage) {
        zoomedImage.src = imageSrc;
        modal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeImageZoom() {
    const modal = document.getElementById('imageZoomModal');

    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
    }
}

// Close zoom modal on escape key or click outside
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageZoom();
    }
});

document.addEventListener('click', function(e) {
    const modal = document.getElementById('imageZoomModal');
    if (e.target === modal) {
        closeImageZoom();
    }
});

// Global function for toggling sidebar (can be called from anywhere)
function toggleSidebar() {
    const communitySidebar = document.querySelector('.community-sidebar');
    const overlay = document.querySelector('.overlay');

    if (communitySidebar && overlay) {
        const isActive = communitySidebar.classList.contains('active');

        console.log('Toggle sidebar - currently active:', isActive);

        if (isActive) {
            // Close sidebar
            communitySidebar.classList.remove('active');
            overlay.classList.add('hidden');
            overlay.classList.remove('active');
            document.body.style.overflow = ''; // Restore scrolling
            console.log('Sidebar closed');
        } else {
            // Open sidebar
            communitySidebar.classList.add('active');
            overlay.classList.remove('hidden');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
            console.log('Sidebar opened');
        }
    }
}

// Navigation functionality
function navigateToSection(section) {
    console.log('Navigating to section:', section);

    // Update active navigation state
    updateActiveNavigation(section);

    // Close mobile sidebar if open
    if (window.innerWidth <= 768) {
        const communitySidebar = document.querySelector('.community-sidebar');
        const overlay = document.querySelector('.overlay');

        if (communitySidebar && overlay && communitySidebar.classList.contains('active')) {
            communitySidebar.classList.remove('active');
            overlay.classList.add('hidden');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Call appropriate function based on section
    switch(section) {
        case 'home-feed':
            if (typeof communityFeed !== 'undefined') {
                communityFeed.showHomeFeed();
            }
            break;
        case 'my-posts':
            if (typeof communityFeed !== 'undefined') {
                communityFeed.showMyPosts();
            }
            break;
        case 'notifications':
            if (typeof communityFeed !== 'undefined') {
                communityFeed.showNotifications();
            }
            break;
        case 'setups':
            // Filter posts by setup category
            if (typeof communityFeed !== 'undefined') {
                communityFeed.currentView = 'feed';
                communityFeed.currentPage = 1;
                communityFeed.hasMore = true;
                communityFeed.updateFeedHeader('Setups & Ideas');
                communityFeed.clearFeed();
                // Set filter to setup and load posts
                const setupFilter = document.querySelector('[data-tab="setup"]');
                if (setupFilter) {
                    setupFilter.click();
                }
            }
            break;
        case 'pnl':
            // Filter posts by P&L category
            if (typeof communityFeed !== 'undefined') {
                communityFeed.currentView = 'feed';
                communityFeed.currentPage = 1;
                communityFeed.hasMore = true;
                communityFeed.updateFeedHeader('P&L Shares');
                communityFeed.clearFeed();
                // Set filter to pnl and load posts
                const pnlFilter = document.querySelector('[data-tab="pnl"]');
                if (pnlFilter) {
                    pnlFilter.click();
                }
            }
            break;
        case 'analysis':
            // Filter posts by analysis category
            if (typeof communityFeed !== 'undefined') {
                communityFeed.currentView = 'feed';
                communityFeed.currentPage = 1;
                communityFeed.hasMore = true;
                communityFeed.updateFeedHeader('Market Analysis');
                communityFeed.clearFeed();
                // Set filter to analysis and load posts
                const analysisFilter = document.querySelector('[data-tab="analysis"]');
                if (analysisFilter) {
                    analysisFilter.click();
                }
            }
            break;
        case 'watchlist':
            // Show watchlist (placeholder for now)
            if (typeof communityFeed !== 'undefined') {
                communityFeed.updateFeedHeader('Watchlist');
                communityFeed.clearFeed();
                // Add watchlist functionality here
                const container = document.getElementById('postsContainer');
                if (container) {
                    container.innerHTML = '<div class="text-center py-8 text-gray-400">Watchlist feature coming soon!</div>';
                }
            }
            break;
        default:
            console.log('Unknown section:', section);
    }
}

function updateActiveNavigation(activeSection) {
    // Remove active class from all navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
        item.classList.remove('bg-gray-700');
        item.classList.add('text-gray-300');
    });

    // Add active class to the selected navigation item
    const activeNav = document.getElementById(`nav-${activeSection}`);
    if (activeNav) {
        activeNav.classList.add('active');
        activeNav.classList.add('bg-gray-700');
        activeNav.classList.remove('text-gray-300');
        activeNav.classList.add('text-white');
    }

    console.log('Updated active navigation to:', activeSection);
}

// Test function for debugging (can be called from browser console)
function testSidebar() {
    console.log('=== SIDEBAR DEBUG TEST ===');
    const communitySidebar = document.querySelector('.community-sidebar');
    const overlay = document.querySelector('.overlay');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');

    console.log('Elements:');
    console.log('- communitySidebar:', communitySidebar);
    console.log('- overlay:', overlay);
    console.log('- mobileSidebarToggle:', mobileSidebarToggle);

    if (communitySidebar) {
        console.log('- sidebar classes:', communitySidebar.className);
        console.log('- sidebar computed style position:', getComputedStyle(communitySidebar).position);
        console.log('- sidebar computed style left:', getComputedStyle(communitySidebar).left);
        console.log('- sidebar computed style zIndex:', getComputedStyle(communitySidebar).zIndex);
    }

    if (overlay) {
        console.log('- overlay classes:', overlay.className);
        console.log('- overlay computed style display:', getComputedStyle(overlay).display);
        console.log('- overlay computed style opacity:', getComputedStyle(overlay).opacity);
    }

    console.log('Window width:', window.innerWidth);
    console.log('=== END DEBUG TEST ===');
}
</script>

<!-- Create Post Modal -->
<div class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="createPostModal">
    <div class="modal-content glass-card rounded-xl p-6 w-full max-w-2xl mx-4">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">Create New Post</h2>
            <button id="closeModalBtn" class="text-gray-400 hover:text-white">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="createPostForm" enctype="multipart/form-data">
            <!-- Post Type Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Post Type</label>
                <div class="flex flex-wrap gap-2 post-type-buttons">
                    <button type="button" class="post-type-btn active bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="general">
                        <i class="fas fa-comment mr-2"></i>General
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="setup">
                        <i class="fas fa-chart-line mr-2"></i>Setup
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="pnl">
                        <i class="fas fa-dollar-sign mr-2"></i>P&L Share
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="analysis">
                        <i class="fas fa-search mr-2"></i>Analysis
                    </button>
                    <button type="button" class="post-type-btn bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-type="educational">
                        <i class="fas fa-graduation-cap mr-2"></i>Educational
                    </button>
                </div>
                <input type="hidden" name="post_type" id="postType" value="general">
            </div>

            <!-- Asset Class Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Asset Class</label>
                <select name="asset_class" id="assetClass" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2">
                    <option value="all">All Assets</option>
                    <option value="equity">Equity</option>
                    <option value="options">Options</option>
                    <option value="futures">Futures</option>
                    <option value="crypto">Crypto</option>
                    <option value="forex">Forex</option>
                </select>
            </div>

            <!-- Title -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                <input type="text" name="title" id="postTitle" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2" placeholder="Enter post title..." required>
            </div>

            <!-- Content -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Content</label>
                <textarea name="content" id="postContent" rows="6" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2" placeholder="Share your thoughts, analysis, or trading insights... Use #hashtags, @mentions, and $symbols" required></textarea>
                <p class="text-xs text-gray-500 mt-1">Tip: Use #hashtags for topics, @mentions for users, and $symbols for stocks</p>
            </div>

            <!-- Image Upload -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Attach Image/Chart (Optional)</label>
                <div class="flex items-center justify-center w-full">
                    <label for="postImage" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-700 transition-colors">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                            <p class="mb-2 text-sm text-gray-400">
                                <span class="font-semibold">Click to upload</span> or drag and drop
                            </p>
                            <p class="text-xs text-gray-500">JPG, PNG, GIF, WebP (MAX. 5MB)</p>
                        </div>
                        <input id="postImage" name="image" type="file" class="hidden" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                    </label>
                </div>
                <div id="imagePreview" class="mt-4 hidden">
                    <div class="relative">
                        <img id="previewImg" src="" alt="Preview" class="max-w-full h-48 object-cover rounded-lg border border-gray-700">
                        <button type="button" id="removeImage" class="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full w-8 h-8 flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        Image will be optimized for web display
                    </p>
                </div>
                <div id="uploadError" class="mt-2 hidden">
                    <p class="text-red-400 text-sm">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span id="uploadErrorMessage"></span>
                    </p>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelPostBtn" class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium">
                    Cancel
                </button>
                <button type="submit" id="submitPostBtn" class="glow-button bg-teal-600 hover:bg-teal-500 text-white px-6 py-2 rounded-lg font-medium">
                    <i class="fas fa-paper-plane mr-2"></i>Post
                </button>
            </div>
        </form>
    </div>
</div>

    </div>
</div>
<!-- End Community Container -->
