<?php

namespace App\Models;

use CodeIgniter\Model;

class RulesModel extends Model
{
    protected $table = 'rules';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = false; // Set to false since existing table doesn't have auto increment
    protected $useSoftDeletes = true;
    protected $useTimestamps = false; // Set to false since we'll handle timestamps manually

    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'id',
        'rule',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $validationRules = [
        'rule' => 'required|max_length[500]'
    ];

    /**
     * Get rules for a specific user (including global rules)
     */
    public function getRulesForUser($userId)
    {
        return $this->where('deleted_at', null)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }


}
