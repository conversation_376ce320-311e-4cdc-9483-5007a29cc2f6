// Rules Management JavaScript
let rulesData = [];
let impactChart = null;

$(document).ready(function () {
    console.log('Rules page loaded, base_url:', typeof base_url !== 'undefined' ? base_url : 'undefined');
    loadRules(); // This will now call loadRulesAnalytics() after rules are loaded
    initializeEventListeners();
});

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Modal controls
    $('#createRuleBtn, #addRuleCard').on('click', openCreateRuleModal);
    $('#closeModalBtn, #cancelRuleBtn').on('click', closeCreateRuleModal);

    // Form submission
    $('#ruleForm').on('submit', handleRuleFormSubmit);

    // Search functionality
    $('#searchRules').on('input', filterRules);

    // Filter buttons
    $('.filter-btn').on('click', handleFilterClick);

    // Delete rule functionality
    $(document).on('click', '.delete-rule-btn', handleDeleteRule);

    // Analytics controls
    $('#refreshAnalytics').on('click', function() {
        $(this).find('i').addClass('fa-spin');
        loadRulesAnalytics();
        setTimeout(() => {
            $(this).find('i').removeClass('fa-spin');
        }, 1000);
    });
    $('#analyticsTimeframe').on('change', loadRulesAnalytics);

    // Close modal when clicking outside
    $('#createRuleModal').on('click', function(e) {
        if (e.target === this) {
            closeCreateRuleModal();
        }
    });
}

/**
 * Load all rules with statistics
 */
function loadRules() {
    $.ajax({
        type: 'GET',
        url: base_url + 'getRulesWithStats',
        dataType: 'json',
        success: function(response) {
            console.log('Rules response:', response);
            if (response && response.success) {
                rulesData = response.rules || [];
                applyFilters(); // Apply current filters instead of showing all
                updateRulesCount(rulesData.length);
                updateFilterCounts(); // Update filter button counts

                // Load analytics after rules are loaded
                loadRulesAnalytics();
            } else {
                console.error('Invalid response:', response);
                showNotification('Error loading rules: ' + (response ? response.message : 'Invalid response'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error Details:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });

            let errorMessage = 'Failed to load rules';
            if (xhr.status === 404) {
                errorMessage = 'Rules endpoint not found (404)';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error (500)';
            } else if (status === 'timeout') {
                errorMessage = 'Request timed out';
            }

            showNotification(errorMessage, 'error');
        }
    });
}

/**
 * Render rules in the grid
 */
function renderRules(rules) {
    const rulesGrid = $('#rulesGrid');
    rulesGrid.empty();
    
    if (rules.length === 0) {
        let emptyMessage = 'No rules found. Create your first trading rule!';

        // Customize message based on current filter
        if (currentFilter === 'most-used') {
            emptyMessage = 'No frequently used rules. Start using your rules to see them here.';
        } else if (currentFilter === 'high-impact') {
            emptyMessage = 'No high-impact rules. Rules with 80%+ impact score will appear here.';
        } else if (currentFilter === 'new') {
            emptyMessage = 'No new rules. Rules created in the last 7 days will appear here.';
        } else if ($('#searchRules').val().trim()) {
            emptyMessage = 'No matching rules. Try adjusting your search terms.';
        }

        rulesGrid.html(`
            <div class="col-span-full text-center py-8">
                <i class="fas fa-list-check text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">${emptyMessage}</p>
            </div>
        `);
        return;
    }
    
    rules.forEach(rule => {
        const ruleCard = createRuleCard(rule);
        rulesGrid.append(ruleCard);
    });
}

/**
 * Create a rule card HTML
 */
function createRuleCard(rule) {
    const adherenceRate = rule.adherence_rate || 0;
    const usageCount = rule.usage_count || 0;

    // Determine card styling based on adherence rate - dark theme with colored shadows
    let cardClasses = 'bg-gray-800 border border-gray-700';
    let statusBadge = '';
    let percentageClasses = '';

    if (adherenceRate >= 90) {
        cardClasses = 'bg-gray-800 border border-gray-700 shadow-lg shadow-emerald-500/20 hover:shadow-emerald-500/30';
        statusBadge = `<span class="inline-flex items-center px-2 py-0.5 bg-emerald-500/20 text-emerald-400 text-xs rounded-md font-medium">
            <i class="fas fa-check w-3 h-3 mr-1"></i> Excellent
        </span>`;
        percentageClasses = 'text-emerald-400 font-bold';
    } else if (adherenceRate >= 70) {
        cardClasses = 'bg-gray-800 border border-gray-700 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30';
        statusBadge = `<span class="inline-flex items-center px-2 py-0.5 bg-blue-500/20 text-blue-400 text-xs rounded-md font-medium">
            <i class="fas fa-trending-up w-3 h-3 mr-1"></i> Good
        </span>`;
        percentageClasses = 'text-blue-400 font-bold';
    } else if (adherenceRate >= 50) {
        cardClasses = 'bg-gray-800 border border-gray-700 shadow-lg shadow-amber-500/20 hover:shadow-amber-500/30';
        statusBadge = `<span class="inline-flex items-center px-2 py-0.5 bg-amber-500/20 text-amber-400 text-xs rounded-md font-medium">
            <i class="fas fa-exclamation-triangle w-3 h-3 mr-1"></i> Needs work
        </span>`;
        percentageClasses = 'text-amber-400 font-bold';
    } else if (usageCount > 0) {
        cardClasses = 'bg-gray-800 border border-gray-700 shadow-lg shadow-red-500/20 hover:shadow-red-500/30';
        statusBadge = `<span class="inline-flex items-center px-2 py-0.5 bg-red-500/20 text-red-400 text-xs rounded-md font-medium">
            <i class="fas fa-times w-3 h-3 mr-1"></i> Poor
        </span>`;
        percentageClasses = 'text-red-400 font-bold';
    } else {
        cardClasses = 'bg-gray-800 border border-gray-700 shadow-lg shadow-gray-500/10 hover:shadow-gray-500/20';
        percentageClasses = 'text-gray-400 font-bold';
    }

    return `
        <div class="${cardClasses} rounded-2xl p-6 transition-all duration-300 cursor-pointer rule-card" data-rule-id="${rule.id}">
            <!-- Header with title and delete button -->
            <div class="flex justify-between items-start mb-4">
                <div class="flex-1">
                    <h3 class="font-semibold text-white text-lg leading-tight">
                        ${rule.name}
                    </h3>
                </div>
                <div class="flex space-x-2 ml-4 flex-shrink-0">
                    <button class="p-2 rounded-lg bg-gray-700 hover:bg-red-900/20 hover:text-red-400 text-gray-400 transition-all duration-200 delete-rule-btn" data-rule-id="${rule.id}" title="Delete Rule">
                        <i class="fas fa-trash w-4 h-4"></i>
                    </button>
                </div>
            </div>

            <!-- Description with fixed height -->
            <div class="mb-6">
                <p class="text-gray-400 text-sm leading-relaxed min-h-[2.5rem] flex items-start">${rule.description || 'No description'}</p>
            </div>

            <!-- Status badge with fixed height -->
            <div class="mb-6 min-h-[1.75rem] flex items-start">
                ${statusBadge || ''}
            </div>

            <!-- Footer with usage and percentage -->
            <div class="flex justify-between items-center pt-4 border-t border-gray-700">
                <div class="flex items-center text-sm text-gray-400 font-medium">
                    <i class="fas fa-chart-line w-3 h-3 mr-2 text-gray-500"></i>
                    Used ${usageCount} times
                </div>
                <div class="flex items-center space-x-2">
                    <div class="text-2xl ${percentageClasses}">
                        ${adherenceRate}%
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Get adherence rate color class
 */
function getAdherenceColor(rate) {
    if (rate >= 90) return 'bg-emerald-100 dark:bg-emerald-900/40 text-emerald-700 dark:text-emerald-300';
    if (rate >= 70) return 'bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300';
    if (rate >= 50) return 'bg-amber-100 dark:bg-amber-900/40 text-amber-700 dark:text-amber-300';
    if (rate > 0) return 'bg-red-100 dark:bg-red-900/40 text-red-700 dark:text-red-300';
    return 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400';
}

/**
 * Format currency with rupee symbol
 */
function formatCurrency(num) {
    if (num === null || num === undefined || isNaN(num)) return '&#8377;0';
    const formatted = Math.abs(num).toLocaleString('en-IN', {maximumFractionDigits: 2});
    return `&#8377;${formatted}`;
}

/**
 * Format currency with sign and rupee symbol
 */
function formatCurrencyWithSign(num) {
    if (num === null || num === undefined || isNaN(num)) return '&#8377;0';
    const sign = num > 0 ? '+' : '';
    const formatted = Math.abs(num).toLocaleString('en-IN', {maximumFractionDigits: 2});
    return `${sign}&#8377;${formatted}`;
}

/**
 * Current active filter
 */
let currentFilter = 'all';

/**
 * Handle filter button clicks
 */
function handleFilterClick() {
    const filterType = $(this).data('filter');
    console.log('Filter button clicked:', filterType);

    // Update active button
    $('.filter-btn').removeClass('active');
    $(this).addClass('active');

    // Update current filter
    currentFilter = filterType;
    console.log('Current filter set to:', currentFilter);

    // Apply filters
    applyFilters();
}

/**
 * Apply current filters (search + category filter)
 */
function applyFilters() {
    console.log('Applying filters. Current filter:', currentFilter, 'Rules data:', rulesData);
    const searchTerm = $('#searchRules').val().toLowerCase();
    let filteredRules = rulesData;

    // Apply search filter
    if (searchTerm) {
        filteredRules = filteredRules.filter(rule =>
            rule.name.toLowerCase().includes(searchTerm) ||
            (rule.description && rule.description.toLowerCase().includes(searchTerm)) ||
            (rule.category && rule.category.toLowerCase().includes(searchTerm))
        );
        console.log('After search filter:', filteredRules.length, 'rules');
    }

    // Apply category filter
    filteredRules = applyRuleFilter(filteredRules, currentFilter);
    console.log('After category filter:', filteredRules.length, 'rules');

    renderRules(filteredRules);
}

/**
 * Filter rules based on search input
 */
function filterRules() {
    applyFilters();
}

/**
 * Apply rule filter based on type
 */
function applyRuleFilter(rules, filterType) {
    console.log('Applying filter:', filterType, 'to rules:', rules);

    switch (filterType) {
        case 'most-used':
            // Filter rules that have been used at least once, then sort by usage frequency
            const usedRules = rules.filter(rule => (rule.usage_count || 0) > 0);
            console.log('Used rules found:', usedRules.length, usedRules);

            if (usedRules.length === 0) {
                // If no rules have been used, show all rules sorted by usage_count anyway
                return rules.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));
            }

            return usedRules.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0));

        case 'high-impact':
            // Filter rules with high adherence rate (80%+) since we don't have impact_score
            const highImpactRules = rules.filter(rule => (rule.adherence_rate || 0) >= 80);
            console.log('High impact rules found:', highImpactRules.length, highImpactRules);
            return highImpactRules;

        case 'new':
            // Show recently created rules (last 7 days)
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const newRules = rules.filter(rule => {
                const createdDate = new Date(rule.created_at);
                return createdDate >= sevenDaysAgo;
            });
            console.log('New rules found:', newRules.length, newRules);
            return newRules;

        case 'all':
        default:
            console.log('Showing all rules:', rules.length);
            return rules;
    }
}

/**
 * Update rules count display
 */
function updateRulesCount(count) {
    $('#totalRulesCount').text(`(${count})`);
}

/**
 * Update filter button counts
 */
function updateFilterCounts() {
    if (!rulesData || rulesData.length === 0) return;

    // Update total count
    $('#totalRulesCount').text(`(${rulesData.length})`);

    // Note: Individual filter counts can be added here if needed in the future
    // For now, we just update the total count
}

/**
 * Open create rule modal
 */
function openCreateRuleModal() {
    $('#createRuleModal').removeClass('hidden');
    $('#ruleName').focus();
}

/**
 * Close create rule modal
 */
function closeCreateRuleModal() {
    $('#createRuleModal').addClass('hidden');
    $('#ruleForm')[0].reset();
}

/**
 * Handle rule form submission
 */
function handleRuleFormSubmit(e) {
    e.preventDefault();

    const formData = {
        name: $('#ruleName').val(),
        description: $('#ruleDescription').val(),
        category: $('#ruleCategory').val()
    };

    $.ajax({
        type: 'POST',
        url: base_url + 'createRule',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('Rule created successfully!', 'success');
                closeCreateRuleModal();
                loadRules();
                loadRulesAnalytics();
            } else {
                showNotification('Error: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('Failed to create rule', 'error');
        }
    });
}

/**
 * Handle delete rule
 */
function handleDeleteRule(e) {
    e.preventDefault();
    e.stopPropagation();

    const ruleId = $(this).data('rule-id');
    const ruleName = $(this).closest('.rule-card').find('h3').text().trim();

    if (!ruleId) {
        showNotification('Error: No rule ID found', 'error');
        return;
    }

    // Show confirmation dialog
    if (typeof showDialog === 'function') {
        showDialog(
            `Are you sure you want to delete the rule "${ruleName}"? This action cannot be undone.`,
            function() {
                deleteRule(ruleId);
            }
        );
    } else {
        // Fallback to confirm dialog
        if (confirm(`Are you sure you want to delete the rule "${ruleName}"? This action cannot be undone.`)) {
            deleteRule(ruleId);
        }
    }
}

/**
 * Delete rule
 */
function deleteRule(ruleId) {
    $.ajax({
        type: 'POST',
        url: base_url + 'deleteRule/' + ruleId,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('Rule deleted successfully!', 'success');
                loadRules();
                loadRulesAnalytics();
            } else {
                showNotification('Error: ' + response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Delete error:', xhr, status, error);
            showNotification('Failed to delete rule', 'error');
        }
    });
}

/**
 * Fetch total trades count for calculating missed opportunities
 */
function fetchTotalTradesCount() {
    return new Promise((resolve) => {
        // Try to get trade count from dashboard metrics API
        $.ajax({
            type: 'POST',
            url: base_url + 'getDashboardMetrics',
            data: { rangeFilter: 1 }, // Use monthly range
            dataType: 'json',
            success: function(response) {
                if (response && response.success && response.data) {
                    const totalTrades = response.data.totalTrades || response.data.total_trades || 0;
                    console.log('Got total trades from dashboard:', totalTrades);
                    resolve(totalTrades);
                } else {
                    console.warn('Could not fetch total trades count from dashboard, using fallback');
                    resolve(calculateFallbackTradeCount());
                }
            },
            error: function() {
                console.warn('Error fetching total trades count, using fallback');
                resolve(calculateFallbackTradeCount());
            }
        });
    });
}

/**
 * Calculate fallback trade count from rules usage
 */
function calculateFallbackTradeCount() {
    if (!rulesData || rulesData.length === 0) return 0;

    // Find the maximum usage count among all rules as a rough estimate
    const maxUsage = Math.max(...rulesData.map(rule => rule.usage_count || 0));
    console.log('Using fallback trade count based on max rule usage:', maxUsage);
    return maxUsage;
}

/**
 * Load rules analytics
 */
function loadRulesAnalytics() {
    console.log('Loading rules analytics...');
    const timeframe = $('#analyticsTimeframe').val() || 30;
    const rangeFilter = timeframe <= 7 ? 0 : timeframe <= 30 ? 1 : timeframe <= 90 ? 2 : 3;

    $.ajax({
        type: 'POST',
        url: base_url + 'getRulesAnalytics',
        data: { rangeFilter: rangeFilter },
        dataType: 'json',
        success: function(response) {
            console.log('Analytics response:', response);
            if (response.success) {
                updateAnalyticsDisplay(response.data);
            } else {
                console.error('Analytics error:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load rules analytics:', xhr, status, error);
        }
    });
}

/**
 * Reset analytics display to default state
 */
function resetAnalyticsDisplay() {
    // Reset card values
    $('#totalRulesFollowed').text('0');
    $('#rulesConsistency').text('0%');
    $('#avgImpactScore').text('0.0');

    // Reset progress bars
    $('#consistencyBar').css('width', '0%');
    $('#mostFollowedBar').css('width', '0%');

    // Reset change indicators with badge styling
    $('#rulesFollowedChange').html('<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0%').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');
    $('#impactScoreChange').html('<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0 pts').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');

    // Reset ratings
    $('#adherenceRating').text('-');
    $('#impactRating').text('No Data');

    // Reset most followed rule
    $('#mostFollowedRule').text('No rules followed yet');
    $('#mostFollowedPercent').text('0%');
    $('#mostFollowedUsage').text('Start following rules to see data');

    // Reset stars
    $('#impactRatingStars .w-2').removeClass('bg-amber-400/80').addClass('bg-slate-300/60 dark:bg-slate-600/60');
}

/**
 * Update analytics display
 */
function updateAnalyticsDisplay(data) {
    console.log('Analytics data received:', data);

    const overall = data.overall || {};
    const mostFollowed = data.mostFollowed || [];
    const impactScores = data.impactScores || [];
    const impact = data.impact || [];

    // Reset all displays to default state first
    resetAnalyticsDisplay();

    // Update overall stats only if data exists
    const totalFollowed = overall.total_followed || 0;
    const consistency = data.consistency || 0;

    if (totalFollowed > 0) {
        $('#totalRulesFollowed').text(totalFollowed);
    }

    if (consistency > 0) {
        $('#rulesConsistency').text(consistency + '%');
        $('#consistencyBar').css('width', consistency + '%');

        // Update adherence rating based on consistency
        let adherenceRating = 'Poor';
        if (consistency >= 90) adherenceRating = 'Excellent';
        else if (consistency >= 75) adherenceRating = 'Good';
        else if (consistency >= 50) adherenceRating = 'Average';
        $('#adherenceRating').text(adherenceRating);
    }

    // Update most followed rule with proper adherence rate
    if (mostFollowed.length > 0 && totalFollowed > 0) {
        const topRule = mostFollowed[0];
        const adherenceRate = topRule.adherence_rate || 0;
        const usageCount = topRule.usage_count || topRule.followed_count || 0;

        $('#mostFollowedRule').text(topRule.name);
        $('#mostFollowedPercent').text(adherenceRate + '%');
        $('#mostFollowedBar').css('width', adherenceRate + '%');

        // Get timeframe for usage text
        const timeframe = $('#analyticsTimeframe').val() || 30;
        let period = 'month';
        if (timeframe <= 7) period = 'week';
        else if (timeframe <= 30) period = 'month';
        else if (timeframe <= 90) period = 'quarter';
        else period = 'year';

        $('#mostFollowedUsage').text(`Used ${usageCount} times this ${period}`);

        // Update color based on adherence rate
        if (adherenceRate >= 75) {
            $('#mostFollowedPercent').removeClass('text-slate-500 dark:text-slate-400').addClass('text-violet-600 dark:text-violet-400');
        } else if (adherenceRate >= 50) {
            $('#mostFollowedPercent').removeClass('text-slate-500 dark:text-slate-400').addClass('text-amber-600 dark:text-amber-400');
        } else if (adherenceRate > 0) {
            $('#mostFollowedPercent').removeClass('text-violet-600 text-amber-600 dark:text-violet-400 dark:text-amber-400').addClass('text-rose-600 dark:text-rose-400');
        }
    }

    // Update change indicators
    const rulesChange = overall.change_followed || 0;
    const impactChange = overall.change_impact || 0;

    // Update rules followed change with enhanced badge styling
    if (rulesChange > 0) {
        $('#rulesFollowedChange').html(`<i class="fas fa-trending-up w-3 h-3 mr-1.5"></i> +${rulesChange}%`);
        $('#rulesFollowedChange').removeClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800');
    } else if (rulesChange < 0) {
        $('#rulesFollowedChange').html(`<i class="fas fa-trending-down w-3 h-3 mr-1.5"></i> ${rulesChange}%`);
        $('#rulesFollowedChange').removeClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800').addClass('bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800');
    } else {
        $('#rulesFollowedChange').html(`<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0%`);
        $('#rulesFollowedChange').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');
    }

    // Calculate and display average impact score
    if (impactScores.length > 0) {
        const avgScore = impactScores.reduce((sum, rule) => sum + (rule.score || 0), 0) / impactScores.length;
        $('#avgImpactScore').text(avgScore.toFixed(1));

        // Update impact rating and stars
        let rating = 'Poor';
        let starCount = 1;
        if (avgScore >= 80) { rating = 'Excellent'; starCount = 5; }
        else if (avgScore >= 70) { rating = 'Good'; starCount = 4; }
        else if (avgScore >= 60) { rating = 'Average'; starCount = 3; }
        else if (avgScore >= 40) { rating = 'Fair'; starCount = 2; }

        $('#impactRating').text(rating);

        // Update star display
        const stars = $('#impactRatingStars .w-2');
        stars.each(function(index) {
            if (index < starCount) {
                $(this).removeClass('bg-slate-300/60 dark:bg-slate-600/60').addClass('bg-amber-400/80');
            } else {
                $(this).removeClass('bg-amber-400/80').addClass('bg-slate-300/60 dark:bg-slate-600/60');
            }
        });
    } else {
        $('#avgImpactScore').text('0.0');
        $('#impactRating').text('No Data');
        $('#impactRatingStars .w-2').removeClass('bg-amber-400/80').addClass('bg-slate-300/60 dark:bg-slate-600/60');
    }

    // Update impact score change with enhanced badge styling
    if (impactChange > 0) {
        $('#impactScoreChange').html(`<i class="fas fa-arrow-up w-3 h-3 mr-1.5"></i> +${impactChange.toFixed(1)} pts`);
        $('#impactScoreChange').removeClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800');
    } else if (impactChange < 0) {
        $('#impactScoreChange').html(`<i class="fas fa-arrow-down w-3 h-3 mr-1.5"></i> ${impactChange.toFixed(1)} pts`);
        $('#impactScoreChange').removeClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800').addClass('bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800');
    } else {
        $('#impactScoreChange').html(`<i class="fas fa-minus w-3 h-3 mr-1.5"></i> 0 pts`);
        $('#impactScoreChange').removeClass('bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800').addClass('bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-slate-200 dark:border-slate-600');
    }

    // Calculate and display P&L metrics
    if (impact.length > 0) {
        const avgPnlFollowed = impact.reduce((sum, rule) => sum + (rule.avg_pnl_followed || 0), 0) / impact.length;
        const avgPnlViolated = impact.reduce((sum, rule) => sum + (rule.avg_pnl_violated || 0), 0) / impact.length;

        if ($('#avgPnlFollowed').length) $('#avgPnlFollowed').html(formatCurrency(avgPnlFollowed));
        if ($('#avgPnlViolated').length) $('#avgPnlViolated').html(formatCurrency(avgPnlViolated));

        // Find most violated and best performing rules
        const mostViolated = impact.reduce((max, rule) => (rule.violated_count > (max.violated_count || 0)) ? rule : max, {});
        const bestPerforming = impact.reduce((max, rule) => ((rule.avg_pnl_followed || 0) > (max.avg_pnl_followed || 0)) ? rule : max, {});

        if ($('#mostViolatedRule').length) $('#mostViolatedRule').text(mostViolated.name || 'None');
        if ($('#bestPerformingRule').length) $('#bestPerformingRule').text(bestPerforming.name || 'None');
    }

    // Update top rules list - sort by usage frequency instead of P&L
    updateTopRulesList(rulesData);

    // Update least used rules section
    let totalTrades = data.totalTrades || overall.total_trades || 0;
    console.log('Analytics data for least used rules:', {
        data: data,
        overall: overall,
        totalTrades: totalTrades,
        rulesDataLength: rulesData?.length
    });

    // If no total trades from analytics, fetch from trades API
    if (totalTrades === 0) {
        fetchTotalTradesCount().then(count => {
            console.log('Fetched total trades count:', count);
            // For testing, use a minimum of 10 trades if we still get 0
            const finalCount = count > 0 ? count : 10;
            console.log('Using final trade count for calculation:', finalCount);
            updateLeastUsedRules(rulesData, finalCount);
        });
    } else {
        updateLeastUsedRules(rulesData, totalTrades);
    }
}

/**
 * Update top rules list
 */
function updateTopRulesList(rules) {
    const container = $('#topRulesList');
    container.empty();

    if (!rules || rules.length === 0) {
        container.html(`
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
                    <i class="fas fa-trophy text-2xl text-slate-400 dark:text-slate-500"></i>
                </div>
                <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2">No Rules Data Available</h3>
                <p class="text-xs text-slate-500 dark:text-slate-400 max-w-xs mx-auto leading-relaxed">
                    Start following your trading rules to see which ones you use most frequently
                </p>
            </div>
        `);
        return;
    }

    // Sort rules by usage frequency (descending) and then alphabetically by name
    const sortedRules = [...rules].sort((a, b) => {
        const usageA = a.usage_count || 0;
        const usageB = b.usage_count || 0;

        if (usageA === usageB) {
            return a.name.localeCompare(b.name);
        }
        return usageB - usageA; // Descending order (most used first)
    });

    // Take the top 5 most used rules
    const topRules = sortedRules.slice(0, 5);
    console.log('Top rules by usage frequency:', topRules);

    topRules.forEach((rule, index) => {
        const usageCount = rule.usage_count || 0;
        const adherenceRate = rule.adherence_rate || 0;

        // Determine color based on usage frequency
        let colorClass = '';
        let badgeColor = '';
        let textColor = '';

        if (usageCount >= 15) {
            // Very high usage - Purple/Violet
            colorClass = 'bg-gradient-to-r from-purple-500/90 to-purple-600/90';
            badgeColor = 'bg-gradient-to-r from-purple-500 to-purple-600';
            textColor = 'text-purple-600 dark:text-purple-400';
        } else if (usageCount >= 10) {
            // High usage - Blue
            colorClass = 'bg-gradient-to-r from-blue-500/90 to-blue-600/90';
            badgeColor = 'bg-gradient-to-r from-blue-500 to-blue-600';
            textColor = 'text-blue-600 dark:text-blue-400';
        } else if (usageCount >= 5) {
            // Medium usage - Green
            colorClass = 'bg-gradient-to-r from-emerald-500/90 to-emerald-600/90';
            badgeColor = 'bg-gradient-to-r from-emerald-500 to-emerald-600';
            textColor = 'text-emerald-600 dark:text-emerald-400';
        } else if (usageCount >= 3) {
            // Low-medium usage - Orange
            colorClass = 'bg-gradient-to-r from-orange-500/90 to-orange-600/90';
            badgeColor = 'bg-gradient-to-r from-orange-500 to-orange-600';
            textColor = 'text-orange-600 dark:text-orange-400';
        } else if (usageCount >= 1) {
            // Low usage - Yellow/Amber
            colorClass = 'bg-gradient-to-r from-amber-500/90 to-amber-600/90';
            badgeColor = 'bg-gradient-to-r from-amber-500 to-amber-600';
            textColor = 'text-amber-600 dark:text-amber-400';
        } else {
            // No usage - Red
            colorClass = 'bg-gradient-to-r from-rose-500/90 to-rose-600/90';
            badgeColor = 'bg-gradient-to-r from-rose-500 to-rose-600';
            textColor = 'text-rose-600 dark:text-rose-400';
        }

        // Enhanced rank badge
        const rankBadge = index < 3 ?
            `<div class="rank-badge ${badgeColor}">${index + 1}</div>` :
            `<div class="rank-badge bg-gradient-to-r from-slate-400 to-slate-500">${index + 1}</div>`;

        const ruleItem = `
            <div class="top-rules-item">
                <div class="flex items-start mb-3">
                    ${rankBadge}
                    <div class="flex-1 ml-3">
                        <h4 class="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-1 leading-tight">${rule.name}</h4>
                        <div class="flex items-center space-x-3">
                            <span class="text-xs text-slate-500 dark:text-slate-400">Used ${usageCount} times</span>
                        </div>
                    </div>
                    <div class="text-right flex-shrink-0">
                        <span class="text-lg font-bold ${textColor}">${usageCount}</span>
                        <div class="text-xs text-slate-500 dark:text-slate-400">times used</div>
                    </div>
                </div>
                <div class="top-rules-bar ${colorClass}" style="width: ${Math.max(20, Math.min(100, (usageCount / Math.max(...topRules.map(r => r.usage_count || 0))) * 100))}%">
                    <span class="text-xs font-medium">${usageCount} uses</span>
                </div>
            </div>
        `;
        container.append(ruleItem);
    });
}

/**
 * Update least used rules section
 */
function updateLeastUsedRules(rulesData, totalTrades = 0) {
    console.log('updateLeastUsedRules called with:', { rulesData, totalTrades, rulesLength: rulesData?.length });
    const container = $('#leastUsedRulesContainer');
    container.empty();

    if (!rulesData || rulesData.length === 0) {
        container.html(`
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
                    <i class="fas fa-exclamation-circle text-2xl text-slate-400 dark:text-slate-500"></i>
                </div>
                <h3 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2">No Rules Available</h3>
                <p class="text-xs text-slate-500 dark:text-slate-400 max-w-xs mx-auto leading-relaxed">
                    Create some trading rules to see usage statistics and identify underutilized rules
                </p>
            </div>
        `);
        return;
    }

    // Sort rules by usage count (ascending) and then alphabetically by name
    const sortedRules = [...rulesData].sort((a, b) => {
        const usageA = a.usage_count || 0;
        const usageB = b.usage_count || 0;

        if (usageA === usageB) {
            return a.name.localeCompare(b.name);
        }
        return usageA - usageB;
    });

    console.log('Sorted rules for least used:', sortedRules);

    // Take the 5 least used rules (or all if fewer than 5)
    const leastUsedRules = sortedRules.slice(0, 5);
    console.log('Least used rules to display:', leastUsedRules);

    leastUsedRules.forEach(rule => {
        const usageCount = rule.usage_count || 0;
        const missedCount = totalTrades - usageCount;
        const missedPercentage = totalTrades > 0 ? ((missedCount / totalTrades) * 100).toFixed(1) : 0;

        console.log(`Rule: ${rule.name}, Used: ${usageCount}, Total Trades: ${totalTrades}, Missed: ${missedCount}, Missed %: ${missedPercentage}%`);

        // Determine colors based on missed percentage (how often rule was NOT used)
        let badgeColor, textColor, statusIcon, colorClass;

        if (usageCount === 0) {
            badgeColor = 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
            textColor = 'text-red-600 dark:text-red-400';
            statusIcon = 'fas fa-times-circle';
            colorClass = 'from-red-500/90 to-red-600/90';
        } else if (missedPercentage >= 80) {
            badgeColor = 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300';
            textColor = 'text-amber-600 dark:text-amber-400';
            statusIcon = 'fas fa-exclamation-triangle';
            colorClass = 'from-amber-500/90 to-amber-600/90';
        } else {
            badgeColor = 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
            textColor = 'text-blue-600 dark:text-blue-400';
            statusIcon = 'fas fa-info-circle';
            colorClass = 'from-blue-500/90 to-blue-600/90';
        }

        const html = `
            <div class="rules-impact-item mb-4">
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-1 pr-4">
                        <h4 class="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-1 leading-tight">${rule.name}</h4>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeColor}">
                                <i class="${statusIcon} w-3 h-3 mr-1"></i>
                                ${usageCount === 0 ? 'Never used' : usageCount === 1 ? 'Used once' : `Used ${usageCount} times`}
                            </span>
                        </div>
                    </div>
                    <div class="text-right flex-shrink-0">
                        <div class="flex items-center justify-end space-x-1 mb-1">
                            <span class="text-lg font-bold ${textColor}">${missedPercentage}%</span>
                        </div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">missed opportunities</div>
                    </div>
                </div>

                <!-- Missed opportunities bar -->
                <div class="w-full bg-slate-100 dark:bg-slate-700 rounded-full h-6 relative overflow-hidden">
                    <div class="h-full bg-gradient-to-r ${colorClass} rounded-full transition-all duration-700 ease-out flex items-center justify-center" style="width: ${Math.max(5, Math.min(100, parseFloat(missedPercentage) || 5))}%">
                        <span class="text-xs font-medium text-white">${missedCount} missed</span>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    if (typeof createToast === 'function') {
        if (type === 'error') {
            createToast('error', 'Error', message);
        } else if (type === 'success') {
            createToast('success', 'Success', message);
        } else {
            createToast('info', 'Info', message);
        }
    } else {
        // Fallback to alert if createToast is not available
        if (type === 'error') {
            alert('Error: ' + message);
        } else {
            alert(message);
        }
    }
}
