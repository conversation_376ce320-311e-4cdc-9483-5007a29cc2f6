<?php

namespace App\Controllers;

use App\Models\RulesModel;
use App\Models\UserModel;

class RulesController extends BaseController
{
    protected $rulesModel;
    protected $userModel;

    public function __construct()
    {
        $this->rulesModel = new RulesModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display the rules page
     */
    public function index()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        // Get decrypted user ID from cookie
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        $data['title'] = 'Trading Rules';
        $data['active'] = 'rules';
        $data['userDetails'] = $this->userModel->find($userId);
        $data['customScript'] = 'rules';
        $data['main_content'] = 'pages/rules';

        return view('includes/template', $data);
    }

    /**
     * Get all rules for the current user
     */
    public function getRules()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            $rules = $this->rulesModel->getRulesForUser($userId);

            return $this->response->setJSON([
                'success' => true,
                'rules' => $rules
            ]);
        } catch (\Exception $e) {
            log_message('error', 'getRules error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Simple test endpoint
     */
    public function testRules()
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Rules endpoint is working',
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $userId ? $userId : 'Not authenticated',
            'cookie_exists' => $this->request->getCookie('user_session') ? 'Yes' : 'No'
        ]);
    }

    /**
     * Create a new rule
     */
    public function createRule()
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $name = $this->request->getPost('name');
        $description = $this->request->getPost('description');
        $category = $this->request->getPost('category');

        if (empty($name)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rule name is required'
            ]);
        }

        try {
            // Get the next available ID
            $db = \Config\Database::connect();
            $maxIdQuery = $db->query("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM rules");
            $maxIdResult = $maxIdQuery->getRow();
            $nextId = $maxIdResult->next_id;

            // For the existing table structure, we'll store the rule name in the 'rule' field
            $ruleText = $name;
            if (!empty($description)) {
                $ruleText .= ' - ' . $description;
            }

            $data = [
                'id' => $nextId,
                'rule' => $ruleText,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($this->rulesModel->insert($data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Rule created successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create rule'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update an existing rule
     */
    public function updateRule($id)
    {
        $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));
        
        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        // Check if rule belongs to user or is global
        $rule = $this->rulesModel->find($id);
        if (!$rule || ($rule['user_id'] !== null && $rule['user_id'] != $userId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rule not found or access denied'
            ]);
        }

        // Only allow updating user's own rules, not global ones
        if ($rule['user_id'] === null) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot modify global rules'
            ]);
        }

        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|max_length[255]',
            'description' => 'permit_empty|max_length[1000]',
            'category' => 'permit_empty|max_length[100]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'category' => $this->request->getPost('category') ?: 'custom'
        ];

        if ($this->rulesModel->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Rule updated successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update rule'
            ]);
        }
    }

    /**
     * Delete a rule
     */
    public function deleteRule($id)
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            if ($id === null || $id === '' || !is_numeric($id)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid rule ID'
                ]);
            }

            // Check if rule exists
            $rule = $this->rulesModel->find($id);
            if (!$rule) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Rule not found'
                ]);
            }

            // For the existing table structure, we'll use soft delete by setting deleted_at
            $deleteData = [
                'deleted_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($this->rulesModel->update($id, $deleteData)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Rule deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete rule'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get rules performance analytics
     */
    public function getRulesAnalytics()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            // Get real analytics data from trade_rules table
            $db = \Config\Database::connect();

            // Get overall statistics
            $totalRuleApplications = $db->table('trade_rules tr')
                ->join('trades t', 't.id = tr.trade_id')
                ->where('t.user_id', $userId)
                ->where('t.deleted_at IS NULL')
                ->countAllResults();

            $totalFollowed = $db->table('trade_rules tr')
                ->join('trades t', 't.id = tr.trade_id')
                ->where('t.user_id', $userId)
                ->where('t.deleted_at IS NULL')
                ->where('tr.followed', 1)
                ->countAllResults();

            $totalViolated = $totalRuleApplications - $totalFollowed;
            $consistency = $totalRuleApplications > 0 ? round(($totalFollowed / $totalRuleApplications) * 100) : 0;

            // Get most followed rules with proper adherence rate calculation
            $mostFollowedQuery = $db->query("
                SELECT r.rule as name, r.id,
                       COUNT(tr.id) as usage_count,
                       SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                       SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                       CASE
                           WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                           ELSE 0
                       END as adherence_rate,
                       AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_when_followed
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE r.deleted_at IS NULL
                GROUP BY r.id, r.rule
                HAVING usage_count > 0
                ORDER BY followed_count DESC, adherence_rate DESC
                LIMIT 5
            ", [$userId]);

            $mostFollowed = $mostFollowedQuery->getResultArray();

            // Get impact data for all rules with usage (using existing table structure)
            $impactQuery = $db->query("
                SELECT r.rule as name, r.id,
                       COUNT(tr.id) as total_usage,
                       SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                       SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                       AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_followed,
                       AVG(CASE WHEN tr.followed = 0 THEN t.pnl_amount ELSE NULL END) as avg_pnl_violated
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE r.deleted_at IS NULL
                GROUP BY r.id, r.rule
                HAVING total_usage > 0
                ORDER BY followed_count DESC
            ", [$userId]);

            $impact = $impactQuery->getResultArray();

            // Calculate impact scores (based on adherence rate and P&L impact)
            $impactScores = [];
            foreach ($impact as $rule) {
                $adherenceRate = $rule['total_usage'] > 0 ? ($rule['followed_count'] / $rule['total_usage']) * 100 : 0;
                $pnlImpact = ($rule['avg_pnl_followed'] ?? 0) - ($rule['avg_pnl_violated'] ?? 0);
                $score = ($adherenceRate * 0.6) + (min(max($pnlImpact / 100, 0), 40)); // Score out of 100

                $impactScores[] = [
                    'name' => $rule['name'],
                    'score' => round($score, 1),
                    'avg_pnl_followed' => round($rule['avg_pnl_followed'] ?? 0, 2),
                    'avg_pnl_violated' => round($rule['avg_pnl_violated'] ?? 0, 2),
                    'followed_count' => (int)($rule['followed_count'] ?? 0),
                    'violated_count' => (int)($rule['violated_count'] ?? 0),
                    'total_usage' => (int)($rule['total_usage'] ?? 0),
                    'adherence_rate' => round($adherenceRate, 1)
                ];
            }

            // Sort by score descending
            usort($impactScores, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            $analyticsData = [
                'overall' => [
                    'total_followed' => $totalFollowed,
                    'total_rule_applications' => $totalRuleApplications,
                    'total_violated' => $totalViolated
                ],
                'consistency' => $consistency,
                'mostFollowed' => $mostFollowed,
                'impact' => $impact,
                'impactScores' => array_slice($impactScores, 0, 10) // Top 10 rules by impact score
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $analyticsData
            ]);
        } catch (\Exception $e) {
            log_message('error', 'getRulesAnalytics error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading analytics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get rules with their usage statistics
     */
    public function getRulesWithStats()
    {
        try {
            $userId = $this->decrypt_cookie_value($this->request->getCookie('user_session'));

            if (!$userId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not authenticated'
                ]);
            }

            // Get rules with real usage statistics
            $db = \Config\Database::connect();

            $rulesQuery = $db->query("
                SELECT r.id, r.rule as name, r.created_at, r.updated_at,
                       COUNT(tr.id) as usage_count,
                       SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) as followed_count,
                       SUM(CASE WHEN tr.followed = 0 THEN 1 ELSE 0 END) as violated_count,
                       CASE
                           WHEN COUNT(tr.id) > 0 THEN ROUND((SUM(CASE WHEN tr.followed = 1 THEN 1 ELSE 0 END) / COUNT(tr.id)) * 100, 1)
                           ELSE 0
                       END as adherence_rate,
                       AVG(CASE WHEN tr.followed = 1 THEN t.pnl_amount ELSE NULL END) as avg_pnl_when_followed
                FROM rules r
                LEFT JOIN trade_rules tr ON r.id = tr.rule_id
                LEFT JOIN trades t ON t.id = tr.trade_id AND t.user_id = ? AND t.deleted_at IS NULL
                WHERE r.deleted_at IS NULL
                GROUP BY r.id, r.rule, r.created_at, r.updated_at
                ORDER BY usage_count DESC, r.rule ASC
            ", [$userId]);

            $transformedRules = [];
            foreach ($rulesQuery->getResultArray() as $rule) {
                $transformedRules[] = [
                    'id' => $rule['id'],
                    'name' => $rule['name'],
                    'description' => '', // No description in old structure
                    'category' => 'general', // Default category
                    'user_id' => null, // All rules are global in old structure
                    'is_active' => 1,
                    'created_at' => $rule['created_at'],
                    'updated_at' => $rule['updated_at'],
                    'usage_count' => (int)$rule['usage_count'],
                    'followed_count' => (int)$rule['followed_count'],
                    'violated_count' => (int)$rule['violated_count'],
                    'adherence_rate' => (float)$rule['adherence_rate'],
                    'avg_pnl_when_followed' => $rule['avg_pnl_when_followed'] ? round((float)$rule['avg_pnl_when_followed'], 2) : 0
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'rules' => $transformedRules
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }
    }
}
